==================================================================
User visible changes for UPX
==================================================================

Changes in 4.2.1 (01 Nov 2023):
  * linux: /proc/self/exe now is optional
  * windows: use SetFileTime to preserve sub-second file timestamps
  * official Windows builds: revert activeCodePage change introduced in 4.2.0
  * bug fixes - see https://github.com/upx/upx/milestone/14

Changes in 4.2.0 (26 Oct 2023):
  * win32/pe and win64/pe: stricter relocation checks; please report regressions
  * unix: use utimensat to preserve sub-second file timestamps
  * new option '--link' to preserve hard-links (Unix only; use with care)
  * add support for NO_COLOR env var; see https://no-color.org/
  * disable macOS support until we fix compatibility with macOS 13+
  * official Windows builds: set activeCodePage to UTF-8
  * bug fixes - see https://github.com/upx/upx/milestone/13

Changes in 4.1.0 (08 Aug 2023):
  * ELF: handle shared libraries with more than 2 PT_LOAD segments
  * bug fixes - see https://github.com/upx/upx/milestone/11

Changes in 4.0.2 (30 Jan 2023):
  * bug fixes - see https://github.com/upx/upx/milestone/9

Changes in 4.0.1 (16 Nov 2022):
  * bug fixes - see https://github.com/upx/upx/milestone/8

Changes in 4.0.0 (28 Oct 2022):
  * Switch to semantic versioning
  * SECURITY NOTES: emphasize the security context in the docs
  * Support easy building from source code with CMake
  * Support easy rebuilding the stubs from source with Podman/Docker
  * Add integrated doctest C++ testing framework
  * Add support for EFI files (PE x86; Kornel Pal)
  * win32/pe and win64/pe: set correct SizeOfHeaders in the PE header
  * bug fixes - see https://github.com/upx/upx/milestone/6
  * bug fixes - see https://github.com/upx/upx/milestone/7

Changes in 3.96 (23 Jan 2020):
  * bug fixes - see https://github.com/upx/upx/milestone/5

Changes in 3.95 (26 Aug 2018):
  * Flag --android-shlib to work around bad design in Android
  * Flag --force-pie when ET_DYN main program is not marked as DF_1_PIE
  * Better compatibility with varying layout of address space on Linux
  * Support for 4 PT_LOAD layout in ELF generated by binutils-2.31
  * bug fixes, particularly better diagnosis of malformed input
  * bug fixes - see https://github.com/upx/upx/milestone/4

Changes in 3.94 (12 May 2017):
  * Add support for arm64-linux (aka "aarch64").
  * Add support for --lzma compression on 64-bit PowerPC (Thierry Fauck).
  * For Mach, "upx -d" will unpack a prefix of the file (and warn).
  * Various improvements to the ELF formats.
  * bug fixes - see https://github.com/upx/upx/milestone/3

Changes in 3.93 (29 Jan 2017):
  * Fixed some win32/pe and win64/pe regressions introduced in 3.92
  * bug fixes - see https://github.com/upx/upx/milestone/2

Changes in 3.92 (11 Dec 2016):
  * INFO: UPX has moved to GitHub - the new home page is https://upx.github.io
  * IMPORTANT: all PE formats: internal changes: reunited the diverged source
    files - please report all regressions into the bug tracker and try UPX 3.91
    in case of problems.
  * Support Apple MacOS 10.12 "Sierra", including more-robust de-compression.
  * Explicitly diagnose Go-language bad PT_LOAD; recommend hemfix.c.
    https://sourceforge.net/p/upx/bugs/195/   https://github.com/pwaller/goupx
  * Fix CERT-FI Case 829767 UPX command line tools segfaults.
    Received by UPX Team on 2015-May-08; originally reported
    by Codenomicon to NCSC-FI on 2015-01-08.
    The vulnerabilities were discovered by Joonas Kuorilehto and
    Antti Häyrynen from Codenomicon.
  * bug fixes - see https://github.com/upx/upx/milestone/1

Changes in 3.91 (30 Sep 2013):
  * Added experimental support for Windows 64-bit PE files, based on
    work by Stefan Widmann. Please use for testing only!
  * bug fixes

==================================================================

Changes in 3.09 (18 Feb 2013):
  * New option --preserve-build-id for GNU ELF.
  * Allow for code signing and LC_UUID on Mac OS X executables.
  * Allow non-contiguous LC_SEGMENTs and 0==.vmsize for Mach-O.
  * Allow zero-filled final page in PackUnix::canUnpack().
  * bug fixes

Changes in 3.08 (12 Dec 2011):
  * Fix allocation in runtime stub for darwin.macho-entry (i386 and amd64).
  * Compress shared library on ELF i386 only [ld.so threatens even this case].
  * Attempt to support ELF on QNX 6.3.0 for armel (experimental).
  * Better diagnostic when ELF -fPIC is needed.
  * PT_NOTE improvements for BSD.
  * Preserve more ELF .e_flags on ARM.
  * Minor code improvements for ELF stubs.
  * Defend against another flavor of corrupt PE header.
  * bug fixes

Changes in 3.07 (08 Sep 2010):
  * win32/pe: fixed relocation handling for files with *no* TLS callbacks
    [severe bug introduced in 3.06]

Changes in 3.06 (04 Sep 2010):
  * win32/pe: TLS callback support contributed by Stefan Widmann. Thanks!
  * bug fixes

Changes in 3.05 (27 Apr 2010):
  * i386-linux and amd64-linux support shared libraries (DT_INIT must
    exist, all info needed by runtime loader must be first in .text, etc.)
  * Linux /proc/self/exe now is preserved by default, by leaving behind
    one page.  New compress-time option --unmap-all-pages is available.
  * Withdraw support for shared libraries on Darwin (Apple Mac OS X)
    because upx does not understand enough about .dylib.
  * bug fixes

Changes in 3.04 (27 Sep 2009):
  * new format Mach/AMD64 supports 64-bit programs on Apple Macintosh.
  * new formats Dylib/i386 and Dylib/ppc32 support shared libraries
    [such as browser plugins] on Darwin (Apple Macintosh).  An existing
    -init function (LC_ROUTINES command) is required.
  * new format vmlinuz/armel for Debian NSLU2 (etc.) linux kernel
  * bvmlinuz boot protocol 2.08 for 386 Linux kernel
  * Extended ABI version 4 for armel-eabi ARM Linux ELF
  * bug fixes

Changes in 3.03 (27 Apr 2008):
  * implement cache flushing for PowerPC (esp. model 440)
  * fix cache flushing on MIPS (>3 MiB compressed, or with holes)
  * fix MIPS big-endian
  * bug fixes

Changes in 3.02 (16 Dec 2007):
  * fix unmapping on arm-linux.elf
  * fix error checking in mmap for i386-linux.elf [triggered by -fPIE]
  * bug fixes

Changes in 3.01 (31 Jul 2007):
  * new options --no-mode, --no-owner and --no-time to disable preservation
    of mode (file permissions), file ownership and timestamps.
  * dos/exe: fixed an incorrect error message caused by a bug in
    relocation handling
  * new format linux/mipsel supports ELF on [32-bit] R3000
  * fix argv[0] on PowerPC with --lzma
  * bug fixes

Changes in 3.00 (27 Apr 2007):
  * watcom/le & tmt/adam: fixed a problem when using certain filters

Changes in 2.93 beta (08 Mar 2007):
  * new formats Mach/i386 and Mach/fat support Mac OS X i686 and
    Universal binaries [i686 and PowerPC only]
  * dos/exe: LZMA is now also supported for 16-bit dos/exe. Please note that
    you have to explicitly use '--lzma' even for '--ultra-brute' here
    because runtime decompression is about 30 times slower than NRV -
    which is really noticeable on old machines.
  * dos/exe: fixed a rarely occurring bug in relocation handling
  * win32/pe & arm/pe: better icon compression handling

Changes in 2.92 beta (23 Jan 2007):
  * new option '--ultra-brute' which tries even more variants
  * slightly improved compression ratio for some files when
    using '--brute' or '--ultra-brute'
  * bug fixes

Changes in 2.91 beta (29 Nov 2006):
  * assorted bug fixes
  * wince/arm: fix "missing" icon & version info resource problem for WinCE 5
  * win32/pe & arm/pe: added option --compress-icons=3 to compress all icons

Changes in 2.90 beta (08 Oct 2006):
  * LZMA algorithm support for most of the 32-bit and 64-bit file formats;
    use new option '--lzma' to enable
  * new format: BSD/elf386 supporting FreeBSD, NetBSD and OpenBSD
    via auto-detection of PT_NOTE or EI_OSABI
  * arm/pe: all the NRV compression methods are now supported
    (only NRV2D is missing in thumb mode)
  * linux/elf386, linux/ElfAMD: remember /proc/self/exe in environment
  * major source code changes: the runtime decompression stubs are now
    built from internal ELF objects

==================================================================

Changes in 2.03 (07 Nov 2006):
  * bvmlinuz/386: fix for kernels not at 0x100000; also allow x86_64
  * linux/elf386: work around Linux kernel bug (0-length .bss needs PF_W)

Changes in 2.02 (13 Aug 2006):
  * linux/386: work around Linux kernel bug (".bss" requires PF_W)
  * linux/ppc32, mach/ppc32: compressed programs now work on a 405 CPU
  * vmlinuz/386: fixed zlib uncompression problem on DOS

Changes in 2.01 (06 Jun 2006):
  * arm/pe: better DLL support
  * dos/exe: device driver support added
  * linux/386: Fix --force-execve for PaX, grSecurity, and strict SELinux.
    /tmp must support execve(); therefore /tmp cannot be mounted 'noexec'.
  * win32/pe & arm/pe: added new option '--keep-resource=' for
    excluding selected resources from compression

Changes in 2.00 (27 Apr 2006):
  * linux/386: the stub now prints an error message if some strict
    SELinux mode does prevent runtime decompression and execution
    (for a fully SELinux-compatible but otherwise inferior compression
    format you can use the '--force-execve' option)
  * linux/386: worked around a problem where certain Linux kernels
    clobber the %ebx register during a syscall
  * win32/pe: disable filters for files with broken PE headers

Changes in 1.96 beta (13 Apr 2006):
  * arm/pe: added filter support
  * win32/pe: removed an unnecessary check so that Delphi 2006 and
    Digital Mars C++ programs finally are supported

Changes in 1.95 beta (09 Apr 2006):
  * arm/pe: added DLL support
  * arm/pe: added thumb mode stub support
  * arm/pe: added unpacking support
  * win32/pe: really worked around R6002 runtime errors

Changes in 1.94 beta (11 Mar 2006):
  * new format: added support for wince/arm (ARM executables running on WinCE)
  * new format: added support for linux elf/amd64
  * new format: added support for linux elf/ppc32
  * new format: added support for mach/ppc32 (Apple Mac OS X)
  * win32/pe: hopefully working "load config" support
  * win32/pe: R6002 runtime errors worked around
  * win32/pe: the stub now clears the dirty stack

Changes in 1.93 beta (07 Feb 2005):
  * vmlinuz/386: fixes to support more kernels

Changes in 1.92 beta (20 Jul 2004):
  * win32/pe: added option '--strip-loadconf' to strip the SEH load
    config section [NOTE: this option is obsolete since UPX 1.94]
  * win32/pe: try to detect .NET (win32/net) files [not yet supported by UPX]
  * vmlinux/386: new format that directly supports building Linux kernels
  * source code: now compiles cleanly under Win64

Changes in 1.91 beta (30 Jun 2004):
  * djgpp2/coff: added support for recent binutils versions
  * linux/elf386, linux/sh386: lots of improvements
  * vmlinuz/386: added support for recent kernels
  * watcom/le: don't crash on files without relocations
  * win32/pe: stricter checks of some PE values
  * option '--brute' now implies '--crp-ms=999999'.
  * source code: much improved portability using ACC, the
    Automatic Compiler Configuration
  * source code: compile fixes for strict ISO C++ compilers
  * source code: compile fixes for Win64
  * re-synced with upx 1.25 branch

Changes in 1.90 beta (11 Nov 2002):
  * implemented several new options for finer compression control:
    '--all-methods', '--all-filters' and '--brute'
  * ps1/exe: new format - UPX now supports PlayStation One programs
  * linux/386: added the option '--force-execve'
  * vmlinuz/386: better kernel detection and sanity checks
  * re-synced with upx 1.24 branch
  * documentation updates

Changes in 1.11 beta (20 Dec 2000):
  * vmlinuz/386: new format - UPX now supports bootable linux kernels
  * linux/elf386: added the new ELF direct-to-memory executable format - no
    more temp files are needed for decompression!
  * linux/sh386: added the new shell direct-to-memory executable format - no
    more temp files are needed for decompression!
  * reduced overall memory requirements during packing
  * quite a number of internal source code rearrangements

==================================================================

Changes in 1.25 (29 Jun 2004)
  * INFO: http://upx.sourceforge.net is the new UPX home page
  * watcom/le: don't crash on files without relocations
  * win32/pe: stricter checks of some PE values
  * source code: much improved portability using ACC, the
    Automatic Compiler Configuration
  * source code: compile fixes for strict ISO C++ compilers
  * source code: compile fixes for Win64

Changes in 1.24 (07 Nov 2002)
  * djgpp2/coff: stricter check of the COFF header to work around a
    problem with certain binutils versions

Changes in 1.23 (05 Sep 2002)
  * atari/tos: fixed an unpacking problem where a buffer was too
    small (introduced in 1.22)
  * linux/386: don't give up too early if a single block turns out
    to be incompressible
  * documentation: added some quick tips how to achieve the best
    compression ratio for the final release of your application
  * fixed a rare situation where the exit code was not set correctly

Changes in 1.22 (27 Jun 2002)
  * atari/tos: the stub now flushes the CPU cache to avoid
    problems on 68030+ machines
  * source code: additional compiler support for Borland C++,
    Digital Mars C++ and Watcom C++

Changes in 1.21 (01 Jun 2002)
  * New option '--crp-ms=' for slightly better compression at the cost
    of higher memory requirements during compression.
    Try 'upx --best --crp-ms=100000'. See the docs for more info.
  * source code: portability fixes
  * source code: compile fixes for g++ 3.0 and g++ 3.1

Changes in 1.20 (23 May 2001)
  * slightly faster compression
  * work around a gcc problem in the latest djgpp2 distribution
  * watcom/le: fixed detection of already compressed files
  * win32/pe: do not compress RT_MANIFEST resource types
  * win32/pe: improved the error message for empty resource sections
  * [NOTE: the jump from 1.08 to 1.20 is to avoid confusion with
           our unstable development releases 1.1x and 1.9x]

Changes in 1.08 (30 Apr 2001)
  * new native port to atari/tos
  * win32/pe: shortened the identstring
  * source code: portability fixes - UPX now builds cleanly under m68k CPUs

Changes in 1.07 (20 Feb 2001)
  * win32/pe: corrected the TLS callback check
  * win32/pe: really fixed that rare bug in relocation handling
  * win32/pe: experimental support for SizeOfHeaders > 0x1000
  * win32/pe: check for superfluous data between sections
  * win32/pe: compressing screensavers (.scr) should finally work

Changes in 1.06 (27 Jan 2001)
  * win32/pe: the check for TLS callbacks introduced in 1.05
      was too strict - disabled for now
  * dos/com: decreased the decompressor stack size a little bit

Changes in 1.05 (24 Jan 2001)
  * win32/pe: refuse to compress programs with TLS callbacks
  * win32/pe: stub changes to avoid slowdowns with some virus monitors
  * win32/pe: reverted the relocation handling changes in 1.04
  * linux/386: dont try to compress Linux kernel images (have a look
      at the unstable UPX 1.1x beta versions for that)

Changes in 1.04 (19 Dec 2000)
  * dos/exe: fixed an internal error when using '--no-reloc'
  * win32/pe: fixed a rare bug in the relocation handling code
  * some tunings for the default compression level

Changes in 1.03 (30 Nov 2000)
  * linked with a new version of the NRV compression library:
      - improved compression ratio a little bit
      - overall significantly faster compression
      - much faster when using high compression levels like '-9' or '--best'
      - much faster with large files
  * atari/tos: added support for FreeMiNT
  * the 32-bit DOS version now uses the new CWSDSTUB extender

Changes in 1.02 (13 Sep 2000)
  * watcom/le: fixed a problem with the Causeway extender
  * win32/pe: don't automatically strip relocs if they seem needed
  * support multiple backup generations when using '-k'
  * updated the console screen driver

Changes in 1.01 (09 Apr 2000)
  * win32/pe: fixed an uncompression problem in DLLs with empty
    fixup sections
  * win32/pe: fixed another rare uncompression problem - a field in the
    PE header was set incorrectly

Changes in 1.00 (26 Mar 2000)
  * documentation updates
  * watcom/le: do not duplicate the non-resident name table
  * win32/pe: fixed an import handling problem: sometimes too much data
    could be deleted from a file -> the uncompressed file would not work
    anymore

Changes in 0.99.3 (07 Mar 2000)
  * win32/pe: fixed a rare problem in the stub string handling part

Changes in 0.99.2 (02 Mar 2000)
  * dos/exe: fixed a typo causing an internal error (introduced in 0.99.1)

Changes in 0.99.1 (29 Feb 2000)
  * win32/pe: fixed some object alignments which were causing
    problems when loading compressed DLLs under Windows NT/2000

Changes in 0.99 (25 Feb 2000)
  * FULL SOURCE CODE RELEASED UNDER THE TERMS OF THE GNU GPL
  * win32/pe: changed default to '--strip-relocs=1'
  * dos/com and dos/sys: fixed a bad decompressor problem
  * linux/386: the counter for the progress indicator was off by one

==================================================================

Changes in 0.94 (06 Dec 1999)
  * win32/pe: the stub now calls ExitProcess in case of import errors
  * under DOS and Windows, the environment variable UPX now accepts
    a '#' as replacement for '=' because of a COMMAND.COM limitation

Changes in 0.93 (22 Nov 1999)
  * win32/pe: fixed --strip-relocs problem with uncompression
  * win32/pe: fixed a bug which could produce a broken decompressor stub
  * linux/386: yet another FreeBSD compatibility fix

Changes in 0.92 (14 Nov 1999)
  * win32/pe: really fixed that one line (see below)

Changes in 0.91 (13 Nov 1999)
  * win32/pe: an important one-line fix for the newly introduced problems
  * dos/com and dos/sys: fixed an internal error
  * dos/exe: correctly restore cs when uncompressing

Changes in 0.90 (10 Nov 1999)
  * all formats: '--overlay=copy' now is the default overlay mode
  * improved compression ratio for most files
  * win32/pe: uncompression is finally supported
  * win32/pe: never compress REGISTRY resources
  * win32/pe: headersize was not set in PE header
  * win32/pe: resource handling is rewritten
  * win32/pe: the last :-) TLS problem is fixed
  * win32/pe: somewhat less memory is required during compression
  * linux/386: fixed compression of scripts which was broken since 0.71
  * linux/386: more FreeBSD compatibility issues
  * changed option: '-i' now prints some more details during compression
    (not finished yet)

Changes in 0.84 (04 Oct 1999)
  * dos/exe: fixed a rare problem where the decompressor could crash
  * some other minor fixes

Changes in 0.83 (17 Sep 1999)
  * dos/exe: fixed minimal memory requirement problem for some files
  * win32/pe: fixed a bug which caused a crash in some compressed files
  * linux/386: various improvements in the stub; also, for the sake
    of FreeBSD users, the stub is now branded as Linux/ELF

Changes in 0.82 (16 Aug 1999)
  * dos/exe: fixed a decompressor bug which could cause crash on some files
  * linux/386: section headers are now stripped from the stub so that
    'strip' won't ruin a compressed file any longer
  * wc/le: support for stack not in the last object disabled again
  * win32/pe: removed some unneeded data

Changes in 0.81 (04 Aug 1999)
  * win32/pe: fixed an important bug in import handling
  * dos/com: fixed an internal error that could happen with very small files

Changes in 0.80 (03 Aug 1999)
  * you can set some default options in the environment var 'UPX'
  * dos/com: the decompressor stub now checks for enough free memory
  * dos/exe: decompressor rewritten, some bugs are fixed
  * dos/exe: new option '--no-reloc': no relocation data is put into
    the DOS header
  * tmt/adam: added support for more stubs, detect already packed files
  * tmt/adam: new option '--copy-overlay'
  * wc/le: reduced memory requirement during uncompression
  * wc/le: support files which do not contain their stack in the last object
  * wc/le: fixed a bug which could cause a crash, improved relocation
    handling
  * wc/le: new option '--copy-overlay'
  * win32/pe: '--compress-icons=2' is now the default
  * win32/pe: even better TLS support
  * win32/pe: versioninfo works on NT
  * win32/pe: import by ordinal from kernel32.dll works
  * win32/pe: other import improvements: importing a nonexistent DLL
    results in a usual Windows message, importing a nonexistent function
    results in program exit (instead of crash ;-)
  * win32/pe: new option: '--compress-resources=0'
  * win32/pe: reduced memory requirement during uncompression, some
    files might even require LESS memory when they're compressed
  * win32/pe: TYPELIBs should work now
  * win32/pe: improved relocation handling, 16-bit relocations should work
  * win32/pe: new option '--strip-relocs' (only if you know what you are doing)
  * win32/pe: new option '--copy-overlay'
  * important internal changes: now the stubs are built at runtime

Changes in 0.72 (12 May 1999)
  * tmt/adam: fixed a serious problem in the decompressor stub; all
    compressed tmt files should be recompressed
  * win32/pe: fixed the 'shared sections not supported' warning:
    read-only shared sections are fine
  * win32/pe: never compress TYPELIB resources
  * win32/pe: compressed files are hopefully less suspicious to heuristic
    virus scanners now
  * linux/386: minor decompressor stub updates, nicer progress bar

Changes in 0.71 (19 Apr 1999)
  * dos/exe: added option '--no-overlay'
  * linux/386: various improvements in the stub, most notably the
    overhead for an extra cleanup process has been removed
  * win32/pe: added support for export forwarders
  * win32/pe: added support for DLLs without entry point or imports
  * win32/pe: yet another .bss fix
  * win32/pe: new option '--compress-icons=2': compress all icons
    which are not in the first icon directory
  * win32/pe: rearranged stub to avoid false alerts from some virus scanners

Changes in 0.70 (30 Mar 1999)
  * added support for linux/386 executables
  * improved compression ratio quite a bit
  * added new compression level '--best' to squeeze out even some more bytes
  * win32/pe: TLS support is much better now
  * win32/pe: --compress-icons=0 should now work as well
  * the usual minor fixes for win32/pe

Changes in 0.62 (16 Mar 1999)
  * win32/pe: --compress-icons and --compress-exports are on now by default
  * win32/pe: --compress-icons should really work now
  * win32/pe: fixed a problem with embedded .bss sections

Changes in 0.61 (08 Mar 1999)
  * atari/tos: fixed a problem where the bss segment could become too small

Changes in 0.60 (06 Mar 1999)
  * win32/pe: fixed file corruption when the size of the export data is invalid
  * win32/pe: fixed a problem with empty resource data
  * win32/pe: compressed file alignment set to minimum value
  * win32/pe: made all compressed sections writable
  * fixed some other win32/pe bugs
  * fixed an address optimization problem for some not Watcom LE files
  * fixed a bug which could make UPX hang when an exe header contained
    an illegal value
  * added some compression flags for the win32/pe format
  * added support for Atari ST/TT executables (atari/tos)
  * improved compression ratio
  * improved compression speed

Changes in 0.51 (14 Jan 1999)
  * fixed a small bug in the PE header that would prevent some compressed
    win32/pe executables from running under Windows NT and WINE

Changes in 0.50 (03 Jan 1999)
  * added support for PE format executables (win32/pe & rtm32/pe)
  * added support for TMT executables (tmt/adam)
  * fixed a dos/sys bug that affected OpenDOS

Changes in 0.40 (05 Oct 1998)
  * improved compression ratio
  * fixed a small but fatal bug in dos/sys introduced in 0.30
  * fixed a rare bug in dos/exe
  * worked around a bug in djgpp's strip 2.8
  * djgpp2/coff: Allegro packfile support should work now
  * added dos/exeh compression method (works on 386+)

Changes in 0.30 (27 Jul 1998)
  * fixed a serious bug in the 32-bit compressors - please don't use
    djgpp2/coff and watcom/le compressed files from previous versions,
    some of them are possibly damaged !
  * the 16-bit uncompressors are a little bit shorter & faster
  * fixed progress indicator for VESA and SVGA text modes

Changes in 0.20 (05 Jul 1998)
  * second public beta release
  * too many changes to list here

Changes in 0.05 (26 May 1998)
  * first public beta release
  * based on experience gained from our previous packers DJP (djgpp2/coff, 1996),
    lzop (1996) and mfxpak (atari/tos, 1990)
