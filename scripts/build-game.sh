#!/bin/bash

# 游戏项目自动化构建脚本
# 用法: ./build-game.sh <游戏ID> <游戏名称> [作者] [描述]
# 示例: ./build-game.sh 500001 "新老虎机" "开发者" "这是一个新的老虎机游戏"

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# 显示标题
show_title() {
    clear
    echo -e "${CYAN}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                     🎮 igame项目构建器 🎮                     ║"
    echo "║                                                              ║"
    echo "║                      快速生成游戏项目模板                      ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
    echo ""
}


# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查参数
if [ $# -lt 2 ]; then
    log_error "参数不足"
    echo "用法: $0 <游戏ID> <游戏名称> [作者] [描述]"
    echo "示例: $0 500001 \"slot\" \"开发者\" \"这是一个新的 slot 游戏\""
    exit 1
fi

GAME_ID=$1
GAME_NAME=$2
AUTHOR=${3:-"Developer"}
DESCRIPTION=${4:-"新生成的游戏项目"}

# 验证游戏ID格式
if ! [[ "$GAME_ID" =~ ^[0-9]+$ ]]; then
    log_error "游戏ID必须是数字"
    exit 1
fi

# 检查游戏ID是否已存在
if [ -f "modules/m${GAME_ID}.go" ]; then
    log_warning "游戏ID ${GAME_ID} 已存在，是否覆盖? (y/N)"
    read -r response
    if [[ ! "$response" =~ ^[Yy]$ ]]; then
        log_info "操作已取消"
        exit 0
    fi
fi
show_title
log_info "开始生成游戏项目..."
log_info "游戏ID: ${GAME_ID}"
log_info "游戏名称: ${GAME_NAME}"
log_info "作者: ${AUTHOR}"
log_info "描述: ${DESCRIPTION}"

# 检查并创建必要的目录
log_info "检查项目目录结构..."
mkdir -p modules
mkdir -p http/games
mkdir -p bin/configs
mkdir -p cmd/gamegen

# 构建gamegen工具
log_info "构建gamegen工具..."
cd cmd/gamegen
if ! go build -o ../../bin/gamegen .; then
    log_error "构建gamegen工具失败"
    exit 1
fi
cd ../..

# 生成游戏项目文件
log_info "生成游戏项目文件..."
if ! ./bin/gamegen "$GAME_ID" "$GAME_NAME" "$AUTHOR" "$DESCRIPTION"; then
    log_error "生成游戏项目文件失败"
    exit 1
fi

# 检查生成的文件
log_info "验证生成的文件..."
MODULE_FILE="modules/m${GAME_ID}.go"
CONFIG_FILE="bin/configs/${GAME_ID}.yaml"
STRUCT_FILE="http/games/s${GAME_ID}.go"

if [ ! -f "$MODULE_FILE" ]; then
    log_error "模块文件生成失败: $MODULE_FILE"
    exit 1
fi

if [ ! -f "$CONFIG_FILE" ]; then
    log_error "配置文件生成失败: $CONFIG_FILE"
    exit 1
fi

if [ ! -f "$STRUCT_FILE" ]; then
    log_error "数据结构文件生成失败: $STRUCT_FILE"
    exit 1
fi

# 编译检查
log_info "编译检查..."
if ! go build -o /dev/null ./modules/; then
    log_warning "模块编译检查失败，可能需要手动调整代码"
fi

if ! go build -o /dev/null ./http/games/; then
    log_warning "游戏数据结构编译检查失败，可能需要手动调整代码"
fi

# 生成测试脚本
log_info "生成测试脚本..."
cat > "scripts/test-game-${GAME_ID}.sh" << EOF
#!/bin/bash

# 游戏 ${GAME_ID} 测试脚本
# 游戏名称: ${GAME_NAME}

set -e

echo "测试游戏 ${GAME_ID}: ${GAME_NAME}"

# 进入bin目录
cd bin

# 生成游戏数据
echo "生成游戏数据..."
if [ -f "./generate.exe" ]; then
    ./generate.exe ${GAME_ID}
elif [ -f "./generate" ]; then
    ./generate ${GAME_ID}
else
    echo "错误: 找不到generate可执行文件"
    echo "请先运行: go build -o bin/generate cmd/generate/main.go"
    exit 1
fi

echo "游戏 ${GAME_ID} 测试完成"
EOF

chmod +x "scripts/test-game-${GAME_ID}.sh"

# 生成README文件
log_info "生成项目文档..."
mkdir -p docs
cat > "docs/game-${GAME_ID}-README.md" << EOF
# 游戏项目: ${GAME_NAME}

## 基本信息
- **游戏ID**: ${GAME_ID}
- **游戏名称**: ${GAME_NAME}
- **作者**: ${AUTHOR}
- **描述**: ${DESCRIPTION}
- **生成时间**: $(date '+%Y-%m-%d %H:%M:%S')

## 文件结构
\`\`\`
modules/m${GAME_ID}.go          # 游戏逻辑模块
http/games/s${GAME_ID}.go       # 游戏数据结构
bin/configs/${GAME_ID}.yaml     # 游戏配置文件
scripts/test-game-${GAME_ID}.sh # 测试脚本
\`\`\`

## 快速开始

### 1. 编译项目
\`\`\`bash
go build -o bin/generate cmd/generate/main.go
\`\`\`

### 2. 生成游戏数据
\`\`\`bash
cd bin
./generate ${GAME_ID}
\`\`\`

### 3. 运行测试
\`\`\`bash
./scripts/test-game-${GAME_ID}.sh
\`\`\`

## 配置说明

### 基础配置
- \`MaxPayout\`: 最大赔付金额
- \`Line\`: 游戏线数
- \`Row\`: 网格行数
- \`Column\`: 网格列数

### 图标配置
- \`IconWeight\`: 各图标出现权重
- \`PayoutTable\`: 赔付表配置
- \`WildIcon\`: 百搭图标ID
- \`ScatterIcon\`: 散布图标ID

### 限制配置
- \`Limit\`: 各赔付等级的样本数限制
- \`MinLimit\`: 最小赔率配置

## 开发指南

### 自定义游戏逻辑
编辑 \`modules/m${GAME_ID}.go\` 文件中的以下方法：
- \`generateGrid()\`: 自定义网格生成逻辑
- \`calculatePayout()\`: 自定义赔付计算逻辑
- \`Spin()\`: 自定义旋转逻辑

### 调整配置
编辑 \`bin/configs/${GAME_ID}.yaml\` 文件调整游戏参数。

### 测试验证
使用测试脚本验证游戏功能：
\`\`\`bash
./scripts/test-game-${GAME_ID}.sh
\`\`\`

## 注意事项
1. 修改配置后需要重新生成游戏数据
2. 确保赔付表配置合理，避免RTP过高或过低
3. 图标权重配置影响游戏体验，需要仔细调试
4. 建议在测试环境充分验证后再部署到生产环境
EOF

# 创建docs目录
mkdir -p docs

log_success "游戏项目生成完成!"
echo ""
log_info "生成的文件:"
echo "  📄 模块文件: modules/m${GAME_ID}.go"
echo "  ⚙️  配置文件: bin/configs/${GAME_ID}.yaml"
echo "  📊 数据结构: http/games/s${GAME_ID}.go"
echo "  🧪 测试脚本: scripts/test-game-${GAME_ID}.sh"
echo "  📖 项目文档: docs/game-${GAME_ID}-README.md"
echo ""
log_info "下一步操作:"
echo "  1. 编译项目: go build -o bin/generate cmd/generate/main.go"
echo "  2. 生成数据: cd bin && ./generate ${GAME_ID}"
echo "  3. 运行测试: ./scripts/test-game-${GAME_ID}.sh"
echo ""
log_success "🎮 游戏开发愉快!"
