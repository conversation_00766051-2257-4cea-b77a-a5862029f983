package test

import (
	"encoding/json"
	"fmt"
	"testing"
)

func TestPattern(t *testing.T) {
	line := `[
    [1, 1, 1, 1, 1],
    [0, 0, 0, 0, 0],
    [2, 2, 2, 2, 2],
    [0, 1, 2, 1, 0],
    [2, 1, 0, 1, 2],
    [1, 2, 2, 2, 1],
    [1, 0, 0, 0, 1],
    [0, 1, 1, 1, 0],
    [2, 1, 1, 1, 2],
    [2, 2, 1, 0, 0],
    [0, 0, 1, 2, 2],
    [2, 1, 1, 1, 0],
    [0, 1, 1, 1, 2],
    [1, 1, 0, 1, 1],
    [1, 1, 2, 1, 1],
    [0, 0, 1, 0, 0],
    [2, 2, 1, 2, 2],
    [1, 2, 1, 2, 1],
    [0, 1, 0, 1, 0],
    [2, 1, 2, 1, 2],
    [1, 0, 1, 0, 1],
    [1, 0, 0, 1, 2],
    [1, 2, 2, 1, 0],
    [1, 1, 0, 1, 2],
    [1, 1, 2, 1, 0]
	]`

	// 解析 JSON 数据
	var lineData [][]int
	err := json.Unmarshal([]byte(line), &lineData)
	if err != nil {
		t.Fatalf("解析 JSON 失败: %v", err)
	}

	// 方法1: 基于数据值的转换
	fmt.Println("=== 方法1: 基于数据值的转换 ===")
	patternOutput1 := convertToPatternFormat(lineData)
	fmt.Println(patternOutput1)

	// 方法2: 标准连线模式转换
	fmt.Println("=== 方法2: 标准连线模式转换 ===")
	patternOutput2 := convertToStandardPatternFormat(lineData)
	fmt.Println(patternOutput2)

	// 方法3: 直接映射转换（推荐）
	fmt.Println("=== 方法3: 直接映射转换（推荐使用）===")
	patternOutput3 := convertToDirectMappingFormat(lineData)
	fmt.Println(patternOutput3)

}

// convertToPatternFormat 将 line 数据转换为符合 @400163.yaml 中 pattern 字段格式的字符串
func convertToPatternFormat(lineData [][]int) string {
	var result string
	result += "Pattern:\n"

	// 根据 400163.yaml 的标准 Pattern 格式，每行代表一个连线模式
	// 位置编码格式：第一位数字表示行（1=第0行，2=第1行，3=第2行）
	// 第二位数字表示列（1=第0列，2=第1列，3=第2列，4=第3列，5=第4列）

	for i, row := range lineData {
		result += fmt.Sprintf("  - [")
		for j, val := range row {
			// 根据数据值确定在3x5网格中的位置
			// val=0 -> 第1行, val=1 -> 第2行, val=2 -> 第3行
			rowPos := (val % 3) + 1 // 将0,1,2映射到1,2,3
			colPos := j + 1         // 列位置 (1-5)
			position := rowPos*10 + colPos

			if j > 0 {
				result += ", "
			}
			result += fmt.Sprintf("%d", position)
		}
		result += fmt.Sprintf("] # %d\n", i+1)
	}

	return result
}

// convertToStandardPatternFormat 使用标准的20线连线模式
func convertToStandardPatternFormat(lineData [][]int) string {
	var result string
	result += "Pattern:\n"

	// 标准的20线连线模式，参考400163.yaml
	standardPatterns := [][]int{
		{21, 22, 23, 24, 25}, // 1 - 中间行
		{11, 12, 13, 14, 15}, // 2 - 上行
		{31, 32, 33, 34, 35}, // 3 - 下行
		{11, 22, 33, 24, 15}, // 4 - 对角线
		{31, 22, 13, 24, 35}, // 5 - 对角线
		{21, 32, 33, 34, 25}, // 6
		{21, 12, 13, 14, 25}, // 7
		{11, 22, 23, 24, 15}, // 8
		{31, 22, 23, 24, 35}, // 9
		{31, 32, 23, 14, 15}, // 10
		{11, 12, 23, 34, 35}, // 11
		{31, 22, 23, 24, 15}, // 12
		{11, 22, 23, 24, 35}, // 13
		{21, 22, 13, 24, 25}, // 14
		{21, 22, 33, 24, 25}, // 15
		{11, 12, 23, 14, 15}, // 16
		{31, 32, 23, 34, 35}, // 17
		{21, 32, 23, 34, 25}, // 18
		{11, 22, 13, 24, 15}, // 19
		{31, 22, 33, 24, 35}, // 20
	}

	// 使用标准模式，忽略输入数据的具体值
	maxLines := len(lineData)
	if maxLines > len(standardPatterns) {
		maxLines = len(standardPatterns)
	}

	for i := 0; i < maxLines; i++ {
		result += fmt.Sprintf("  - [")
		for j, pos := range standardPatterns[i] {
			if j > 0 {
				result += ", "
			}
			result += fmt.Sprintf("%d", pos)
		}
		result += fmt.Sprintf("] # %d\n", i+1)
	}

	return result
}

// convertToDirectMappingFormat 直接将数据映射为连线位置（推荐方法）
func convertToDirectMappingFormat(lineData [][]int) string {
	var result string
	result += "Pattern:\n"

	for i, row := range lineData {
		result += fmt.Sprintf("  - [")
		for j, val := range row {
			// 直接将数据值映射为位置编码
			// 这里假设：
			// - 数据中的值0,1,2代表不同的行位置偏好
			// - 但最终还是要生成标准的3x5网格位置编码

			// 根据列位置和数据值生成位置编码
			var rowPos int
			switch val {
			case 0:
				rowPos = 1 // 第1行
			case 1:
				rowPos = 2 // 第2行
			case 2:
				rowPos = 3 // 第3行
			default:
				rowPos = 2 // 默认第2行
			}

			colPos := j + 1 // 列位置 (1-5)
			position := rowPos*10 + colPos

			if j > 0 {
				result += ", "
			}
			result += fmt.Sprintf("%d", position)
		}
		result += fmt.Sprintf("] # %d\n", i+1)
	}

	return result
}
