package test

import (
	"encoding/json"
	"fmt"
	"reflect"
	"sort"
	"strings"
	"testing"
)

// JSONToGoMapString 将JSON字符串转换为Go语言map[string]any{}格式的字符串
func JSONToGoMapString(jsonStr string) (string, error) {
	var data any
	if err := json.Unmarshal([]byte(jsonStr), &data); err != nil {
		return "", fmt.Errorf("解析JSON失败: %v", err)
	}

	return convertToGoMapString(data, 0), nil
}

// convertToGoMapString 递归转换任意类型为Go map字符串格式
func convertToGoMapString(data any, indent int) string {
	if data == nil {
		return "nil"
	}

	v := reflect.ValueOf(data)
	switch v.Kind() {
	case reflect.Map:
		return convertMapToGoString(data.(map[string]any), indent)
	case reflect.Slice:
		return convertSliceToGoString(data.([]any), indent)
	case reflect.String:
		return fmt.Sprintf(`"%s"`, data.(string))
	case reflect.Bool:
		return fmt.Sprintf("%t", data.(bool))
	case reflect.Float64:
		// JSON数字默认解析为float64
		f := data.(float64)
		if f == float64(int64(f)) {
			return fmt.Sprintf("%d", int64(f))
		}
		return fmt.Sprintf("%g", f)
	default:
		return fmt.Sprintf("%v", data)
	}
}

// convertMapToGoString 转换map为Go字符串格式
func convertMapToGoString(m map[string]any, indent int) string {
	if len(m) == 0 {
		return "map[string]any{}"
	}

	var builder strings.Builder
	builder.WriteString("map[string]any{\n")

	// 对键进行排序以保证输出一致性
	keys := make([]string, 0, len(m))
	for k := range m {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	for _, key := range keys {
		value := m[key]
		indentStr := strings.Repeat("\t", indent+1)
		builder.WriteString(fmt.Sprintf(`%s"%s": %s,`+"\n", indentStr, key, convertToGoMapString(value, indent+1)))
	}

	builder.WriteString(strings.Repeat("\t", indent) + "}")
	return builder.String()
}

// convertSliceToGoString 转换slice为Go字符串格式
func convertSliceToGoString(s []any, indent int) string {
	if len(s) == 0 {
		return "[]any{}"
	}

	// 检查slice中所有元素的类型以确定最合适的类型
	sliceType := determineSliceType(s)

	var builder strings.Builder
	builder.WriteString(sliceType + "{")

	// 如果是简单类型且元素不多，可以放在一行
	if isSimpleSlice(s) && len(s) <= 10 {
		for i, item := range s {
			if i > 0 {
				builder.WriteString(", ")
			}
			builder.WriteString(convertToGoMapString(item, indent))
		}
	} else {
		// 复杂类型或元素较多，分行显示
		builder.WriteString("\n")
		for _, item := range s {
			indentStr := strings.Repeat("\t", indent+1)
			builder.WriteString(fmt.Sprintf("%s%s,\n", indentStr, convertToGoMapString(item, indent+1)))
		}
		builder.WriteString(strings.Repeat("\t", indent))
	}

	builder.WriteString("}")
	return builder.String()
}

// determineSliceType 确定slice的Go类型
func determineSliceType(s []any) string {
	if len(s) == 0 {
		return "[]any"
	}

	// 检查是否所有元素都是同一类型
	firstType := reflect.TypeOf(s[0])
	allSameType := true

	for _, item := range s {
		if reflect.TypeOf(item) != firstType {
			allSameType = false
			break
		}
	}

	if !allSameType {
		return "[]any"
	}

	// 根据第一个元素的类型确定slice类型
	switch s[0].(type) {
	case string:
		return "[]string"
	case bool:
		return "[]bool"
	case float64:
		// 检查是否都是整数
		allInts := true
		for _, item := range s {
			if f, ok := item.(float64); ok {
				if f != float64(int64(f)) {
					allInts = false
					break
				}
			}
		}
		if allInts {
			return "[]int"
		}
		return "[]float64"
	case map[string]any:
		return "[]map[string]any"
	default:
		return "[]any"
	}
}

// isSimpleSlice 判断是否为简单类型的slice
func isSimpleSlice(s []any) bool {
	for _, item := range s {
		switch item.(type) {
		case map[string]any, []any:
			return false
		}
	}
	return true
}

// TestJSONToGoMapString 测试JSON转换为Go map字符串的功能
func TestJSONToGoMapString(t *testing.T) {
	// 测试用例1：简单对象
	jsonStr1 := `{
		"gamegroup": "base",
		"doubleAssortment": ["off"],
		"maxBetPerGame_cents": null,
		"betAssortment": [1, 2, 3, 4, 5, 8, 10, 15, 20, 25, 50, 75, 100]
	}`

	result1, err := JSONToGoMapString(jsonStr1)
	if err != nil {
		t.Fatalf("转换失败: %v", err)
	}

	fmt.Println("=== 测试用例1：简单对象 ===")
	fmt.Println(result1)
	fmt.Println()

	// 测试用例2：复杂嵌套对象
	jsonStr2 := `{
		"winValidation": {
			"needcheck": false,
			"winlimit_fictiveRotate_gcurr": 25000000,
			"remaintime": 86400000,
			"period": 86400000,
			"isApproved": false,
			"isNotApproved": false,
			"isWaitApprove": false
		},
		"buyBonus": {
			"wasBuy": 0,
			"selectId": -1,
			"buyTotalBetK": [
				{"id": 0, "cost": 100, "prefix2": "_BASE_FG", "rtp": 96.23},
				{"id": 1, "cost": 500, "prefix2": "_BASE_FG_HOT", "rtp": 96.25}
			]
		}
	}`

	result2, err := JSONToGoMapString(jsonStr2)
	if err != nil {
		t.Fatalf("转换失败: %v", err)
	}

	fmt.Println("=== 测试用例2：复杂嵌套对象 ===")
	fmt.Println(result2)
	fmt.Println()

	// 测试用例3：二维数组
	jsonStr3 := `{
		"startBox": [
			[8, 1, 1, 10, 11],
			[8, 8, 3, 10, 4],
			[5, 4, 8, 10, 8]
		],
		"paytable": [
			[[0, 8]],
			[[1, 1]],
			[[2, 8]]
		]
	}`

	result3, err := JSONToGoMapString(jsonStr3)
	if err != nil {
		t.Fatalf("转换失败: %v", err)
	}

	fmt.Println("=== 测试用例3：二维数组 ===")
	fmt.Println(result3)
	fmt.Println()
}

// ExampleJSONToGoMapString 示例使用函数
func ExampleJSONToGoMapString() {
	// 示例JSON字符串
	jsonStr := `{
		"gamegroup": "base",
		"doubleAssortment": ["off"],
		"maxBetPerGame_cents": null,
		"betAssortment": [1, 2, 3, 4, 5, 8, 10, 15, 20, 25, 50, 75, 100],
		"winValidation": {
			"needcheck": false,
			"winlimit_fictiveRotate_gcurr": 25000000,
			"remaintime": 86400000
		},
		"startBox": [
			[8, 1, 1, 10, 11],
			[8, 8, 3, 10, 4],
			[5, 4, 8, 10, 8]
		]
	}`

	// 转换为Go map字符串格式
	result, err := JSONToGoMapString(jsonStr)
	if err != nil {
		fmt.Printf("转换失败: %v\n", err)
		return
	}

	fmt.Println("转换结果:")
	fmt.Println(result)
}
