package test

import (
	"encoding/json"
	"fmt"
)

// PatternConverter 提供将line数据转换为Pattern格式的工具函数
type PatternConverter struct{}

// ConvertLineToPattern 将JSON格式的line数据转换为符合Pattern格式的字符串
// 这是主要的转换函数，推荐使用
func (pc *PatternConverter) ConvertLineToPattern(lineJSON string) (string, error) {
	var lineData [][]int
	err := json.Unmarshal([]byte(lineJSON), &lineData)
	if err != nil {
		return "", fmt.Errorf("解析JSON失败: %v", err)
	}

	return pc.ConvertDataToPattern(lineData), nil
}

// ConvertDataToPattern 将二维数组数据转换为Pattern格式
func (pc *PatternConverter) ConvertDataToPattern(lineData [][]int) string {
	var result string
	result += "Pattern:\n"
	
	// 根据400163.yaml的Pattern格式进行转换
	// 位置编码：第一位数字表示行（1=第0行，2=第1行，3=第2行）
	// 第二位数字表示列（1=第0列，2=第1列，3=第2列，4=第3列，5=第4列）
	
	for i, row := range lineData {
		result += "  - ["
		for j, val := range row {
			// 根据数据值确定在3x5网格中的位置
			// val=0 -> 第1行, val=1 -> 第2行, val=2 -> 第3行
			rowPos := (val % 3) + 1  // 将0,1,2映射到1,2,3
			colPos := j + 1          // 列位置 (1-5)
			position := rowPos*10 + colPos
			
			if j > 0 {
				result += ", "
			}
			result += fmt.Sprintf("%d", position)
		}
		result += fmt.Sprintf("] # %d\n", i+1)
	}
	
	return result
}

// ConvertToStandardPattern 使用标准的20线连线模式（忽略输入数据的具体值）
func (pc *PatternConverter) ConvertToStandardPattern(lineData [][]int) string {
	var result string
	result += "Pattern:\n"
	
	// 标准的20线连线模式，完全参考400163.yaml
	standardPatterns := [][]int{
		{21, 22, 23, 24, 25}, // 1 - 中间行
		{11, 12, 13, 14, 15}, // 2 - 上行
		{31, 32, 33, 34, 35}, // 3 - 下行
		{11, 22, 33, 24, 15}, // 4 - 对角线
		{31, 22, 13, 24, 35}, // 5 - 对角线
		{21, 32, 33, 34, 25}, // 6
		{21, 12, 13, 14, 25}, // 7
		{11, 22, 23, 24, 15}, // 8
		{31, 22, 23, 24, 35}, // 9
		{31, 32, 23, 14, 15}, // 10
		{11, 12, 23, 34, 35}, // 11
		{31, 22, 23, 24, 15}, // 12
		{11, 22, 23, 24, 35}, // 13
		{21, 22, 13, 24, 25}, // 14
		{21, 22, 33, 24, 25}, // 15
		{11, 12, 23, 14, 15}, // 16
		{31, 32, 23, 34, 35}, // 17
		{21, 32, 23, 34, 25}, // 18
		{11, 22, 13, 24, 15}, // 19
		{31, 22, 33, 24, 35}, // 20
	}
	
	// 使用标准模式，行数以输入数据为准，但最多20行
	maxLines := len(lineData)
	if maxLines > len(standardPatterns) {
		maxLines = len(standardPatterns)
	}
	
	for i := 0; i < maxLines; i++ {
		result += "  - ["
		for j, pos := range standardPatterns[i] {
			if j > 0 {
				result += ", "
			}
			result += fmt.Sprintf("%d", pos)
		}
		result += fmt.Sprintf("] # %d\n", i+1)
	}
	
	return result
}

// GetPatternExplanation 返回Pattern格式的说明
func (pc *PatternConverter) GetPatternExplanation() string {
	return `
Pattern格式说明：
1. Pattern字段定义了游戏的连线模式
2. 每个数组代表一条连线，包含5个位置编码
3. 位置编码格式：两位数字XY
   - X: 行号（1=第0行，2=第1行，3=第2行）
   - Y: 列号（1=第0列，2=第1列，3=第2列，4=第3列，5=第4列）
4. 例如：21表示第1行第0列，33表示第2行第2列

示例：
Pattern:
  - [21, 22, 23, 24, 25] # 中间行连线
  - [11, 12, 13, 14, 15] # 上行连线  
  - [31, 32, 33, 34, 35] # 下行连线
`
}

// NewPatternConverter 创建新的转换器实例
func NewPatternConverter() *PatternConverter {
	return &PatternConverter{}
}
