# Line 数据转换为 Pattern 格式工具

## 概述

本工具用于将测试文件中的 `line` 变量数据转换为符合 `belatra` 定义的 `Pattern` 字符串格式。

## Pattern 格式说明

### 基本结构

```yaml
Pattern:
    - [21, 22, 23, 24, 25] # 1 - 连线1
    - [11, 12, 13, 14, 15] # 2 - 连线2
    - [31, 32, 33, 34, 35] # 3 - 连线3
    # ... 更多连线
```

### 位置编码规则

-   每个数字是两位数的位置编码：`XY`
-   `X`：行号（1=第 0 行，2=第 1 行，3=第 2 行）
-   `Y`：列号（1=第 0 列，2=第 1 列，3=第 2 列，4=第 3 列，5=第 4 列）

### 示例

-   `21`：第 1 行第 0 列（中间行最左边）
-   `33`：第 2 行第 2 列（下行中间）
-   `15`：第 0 行第 4 列（上行最右边）

## 使用方法

### 1. 使用 PatternConverter 工具类（推荐）

```go
package main

import (
    "fmt"
    "log"
)

func main() {
    // 你的line数据
    lineJSON := `[
        [1, 1, 1, 1, 1],
        [0, 0, 0, 0, 0],
        [2, 2, 2, 2, 2]
    ]`

    // 创建转换器
    converter := NewPatternConverter()

    // 转换
    result, err := converter.ConvertLineToPattern(lineJSON)
    if err != nil {
        log.Fatalf("转换失败: %v", err)
    }

    fmt.Println(result)
}
```

### 2. 直接调用转换函数

```go
// 解析JSON数据
var lineData [][]int
json.Unmarshal([]byte(lineJSON), &lineData)

// 转换
converter := NewPatternConverter()
result := converter.ConvertDataToPattern(lineData)
fmt.Println(result)
```

## 转换方法

### 方法 1：基于数据值的转换

-   根据 line 数据中的值（0,1,2）确定行位置
-   `0` → 第 1 行，`1` → 第 2 行，`2` → 第 3 行
-   列位置按数组索引确定

### 方法 2：标准连线模式转换

-   使用 400163.yaml 中的标准 20 线连线模式
-   忽略输入数据的具体值
-   适用于需要标准连线配置的场景

### 方法 3：直接映射转换（推荐）

-   与方法 1 相同，但提供了更清晰的接口
-   推荐在生产环境中使用

## 文件说明

-   `pattern_test.go`：测试文件，展示所有转换方法
-   `pattern_converter.go`：转换器工具类
-   `pattern_example.go`：使用示例
-   `README_Pattern_Conversion.md`：本说明文档

## 运行测试

```bash
cd test
go test -v -run TestPattern
```

## 输出示例

转换后的 Pattern 格式：

```yaml
Pattern:
    - [21, 22, 23, 24, 25] # 1
    - [11, 12, 13, 14, 15] # 2
    - [31, 32, 33, 34, 35] # 3
    - [11, 22, 33, 24, 15] # 4
    - [31, 22, 13, 24, 35] # 5
    # ... 更多连线
```

## 注意事项

1. 确保输入的 line 数据是有效的 JSON 格式
2. 数据中的值应该是 0、1、2，代表不同的行位置
3. 每行应该包含 5 个元素，对应 5 列
4. 转换后的 Pattern 可以直接复制到 bin/configs/x.yaml 文件中使用

## 扩展功能

如需自定义转换逻辑，可以：

1. 修改`PatternConverter`类中的转换算法
2. 添加新的转换方法
3. 调整位置编码规则

## 技术细节

-   使用 Go 语言实现
-   支持 JSON 解析
-   提供多种转换策略
-   包含完整的错误处理
