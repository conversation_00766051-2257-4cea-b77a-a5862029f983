package test

import (
	"bytes"
	"compress/gzip"
	"encoding/base64"
	"fmt"
	"io"
	"log"
	"testing"
)

func CompressJSON(b []byte) (string, error) {
	var buf bytes.Buffer
	writer := gzip.NewWriter(&buf)
	if _, err := writer.Write(b); err != nil {
		log.Printf("Gzip compression error: %v", err)
		writer.Close()
		return "", err
	}
	if err := writer.Close(); err != nil {
		log.Printf("Gzip writer close error: %v", err)
		return "", err
	}
	compressedBase64 := base64.StdEncoding.EncodeToString(buf.Bytes())
	return compressedBase64, nil
}

// DecompressJSON 将Base64编码的Gzip压缩数据解码并解压缩为原始JSON字节切片
func DecompressJSON(compressedBase64 string) ([]byte, error) {
	// 1. Base64解码
	compressedBytes, err := base64.StdEncoding.DecodeString(compressedBase64)
	if err != nil {
		log.Printf("Base64 decode error: %v", err)
		return nil, err
	}

	// 2. 创建Gzip读取器
	reader, err := gzip.NewReader(bytes.NewReader(compressedBytes))
	if err != nil {
		log.Printf("Gzip reader creation error: %v", err)
		return nil, err
	}
	defer reader.Close()

	// 3. 读取解压缩后的数据
	var buf bytes.Buffer
	if _, err := io.Copy(&buf, reader); err != nil {
		log.Printf("Gzip decompression error: %v", err)
		return nil, err
	}

	// 4. 返回解压缩后的字节切片
	return buf.Bytes(), nil
}

func TestBase64(t *testing.T) {
	encoded := "H4sIAAAAAAAA/0RQS2/bMAz+L9+ZB1p+SsemQLGhKwYEOw09yDKdGZGtQY9uQ5H/PsTJVp4I8nuR73BhEhgmTDZbmHc8SH5+g6kIh7Bl+Z1htuI94VBilM39gcG34yM+GE8+TF+Kz8tPLzvxZXHnF7sKDGJbq65WIIySjy5ESTDfK2ZSzNQyvxJOwU8wmrumZoL/bN35a/gAvxJKkrhMMBWrhjXh1+LCBgNcCCe7ym23lyJ4eRO/jwjrsj3cjW8QwppO/04K+YfEJ7vKp8f9B1FS8XknZjt6uYvkcJar3TzMQzW2E8swt8rOulcts+jJ9d3YNfXotJr1MNS6HdXMWinbN9Lpqh/mqXcz/c9Y3VpctbP1z8smh1C2qzVf7hFxLM5JSiCkbHNJMDkWufwNAAD//xFBzS20AQAA"

	b, _ := DecompressJSON(encoded)
	fmt.Println(string(b))
}
