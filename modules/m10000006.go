package modules

import (
	"encoding/json"
	"igameCommon/basic"
	"igameCommon/utils"
	"igameHttp/games"
	"math/rand"
)

type c10000006 struct {
	Row            int               // 网格行数
	Column         int               // 网格列数
	ScatterIcon    int32             //夺宝符号
	PayoutTable    map[int32][][]int // 反奖率对照表
	IconWeight     map[int32]int
	FreeIconWeight map[int32]int
	MultipleWeight map[int32]int  //翻倍符号权重
	FreeSpinConfig map[string]int //免费旋转配置
}

var _ = Factory.reg(basic.NewGeneral[*m10000006])

type m10000006 struct {
	Config               c10000006
	RandByWeight         *utils.RandomWeightPicker[int32, int]
	RandByFreeIconWeight *utils.RandomWeightPicker[int32, int]
	RandByMultipleWeight *utils.RandomWeightPicker[int32, int] //翻倍符号
}

func (m m10000006) ClientMode() int32 {
	return basic.EnumClientMode.ONE
}

func (m10000006) ID() int32 {
	return 10000006
}

func (m *m10000006) Init(config []byte) {
	m.Config = utils.ParseYAML[c10000006](config)
	m.RandByWeight = utils.NewRandomWeightPicker(m.Config.IconWeight)
	m.RandByFreeIconWeight = utils.NewRandomWeightPicker(m.Config.FreeIconWeight)
	// 翻倍符号权重
	m.RandByMultipleWeight = utils.NewRandomWeightPicker(m.Config.MultipleWeight)

}

func (m *m10000006) ZeroSpin(ctl int32, rd *rand.Rand) basic.ISpin {
	for {
		spin := m.Spin(rand.New(rd))
		pay := spin.Payout()
		if pay == 0 {
			return spin
		}
	}
}

func (m m10000006) Spin(rd *rand.Rand) basic.ISpin {
	grid, carmuls := m.genGrid(rd, false)
	spin := &games.S10000006{
		GameId:  m.ID(),
		Line:    m.Line(),
		Row:     m.Config.Row,
		Column:  m.Config.Column,
		Carmuls: carmuls,
	}

	m.parsePayout(grid, spin, rd)
	spin.Rounds = append(spin.Rounds, spin)

	// 检查是否触发免费旋转
	scatterCount := spin.Pages[len(spin.Pages)-1].ScatterCount
	if totalfree := m.getFreeSpinCount(scatterCount, false); totalfree > 0 {
		spin.IsStartFree = true
		spin.FreeSpins = int32(totalfree)
	}

	if spin.IsStartFree {
		for ; spin.FreeSpins > 0; spin.FreeSpins-- {
			freeGrid, freeCarmuls := m.genGrid(rd, true)
			freespin := &games.S10000006{
				GameId:      m.ID(),
				Line:        m.Line(),
				Row:         m.Config.Row,
				Column:      m.Config.Column,
				FreeSpins:   0,
				IsFreeModel: true,
				Carmuls:     freeCarmuls,
			}
			m.parsePayout(freeGrid, freespin, rd)
			spin.Pays += freespin.Pays

			// 触发的免费旋转次数
			scatterCount = freespin.Pages[len(freespin.Pages)-1].ScatterCount
			if totalfree := m.getFreeSpinCount(scatterCount, true); totalfree > 0 {
				freespin.FreeSpins = int32(totalfree)
			}

			spin.FreeSpins += freespin.FreeSpins
			spin.Rounds = append(spin.Rounds, freespin)
		}
	}

	return spin
}

// carmuls : 最后一行的翻倍符号
func (m *m10000006) genGrid(rd *rand.Rand, isFreeModel bool) (grid []int32, carmuls []int32) {
	if !isFreeModel {
		grid = m.RandByWeight.More(m.Config.Row*m.Config.Column, rd)
		// 最后一排的翻倍符号
		carmuls = make([]int32, m.Config.Column)
		// 任选3个位置, 设置翻倍符号
		idxlist := make([]int, 0)
		for i := 0; i < m.Config.Column; i++ {
			idxlist = append(idxlist, i)
		}
		utils.Shuffle[int](idxlist, rd)
		for _, idx := range idxlist[:3] {
			carmuls[idx] = int32(m.RandByMultipleWeight.One(rd))
		}
	} else {
		grid = m.RandByFreeIconWeight.More(m.Config.Row*m.Config.Column, rd)
		// 最后一排的翻倍符号
		// 免费模式的最后一排每个位置都有翻倍符号
		carmuls = m.RandByMultipleWeight.More(m.Config.Column, rd)
	}
	return
}

// 获取免费旋转次数
func (m *m10000006) getFreeSpinCount(scatterCount int, isFreeModel bool) (totalfree int) {
	if !isFreeModel {
		if scatterCount >= m.Config.FreeSpinConfig["MinCount"] {
			basefree := 10 //免费次数 10次
			// 每个额外的scatter再多触发2次免费旋转
			extrafree := m.Config.FreeSpinConfig["ExtraCount"] * (scatterCount - m.Config.FreeSpinConfig["MinCount"])
			totalfree = basefree + extrafree
		}
	} else {
		if scatterCount >= m.Config.FreeSpinConfig["MinCount"] {
			basefree := 10 //免费次数 10次
			// 每个额外的scatter再多触发2次免费旋转
			extrafree := m.Config.FreeSpinConfig["ExtraCount"] * (scatterCount - m.Config.FreeSpinConfig["MinCount"])
			totalfree = basefree + extrafree
		} else if scatterCount == 2 {
			totalfree = 5
		}
	}
	return
}

func (m *m10000006) parsePayout(grid []int32, spin *games.S10000006, rd *rand.Rand) {
	currentGrid := make([]int32, len(grid))
	copy(currentGrid, grid)

	for {
		scatterCount := 0 //记录夺宝图标数
		for _, icon := range currentGrid {
			if icon == m.Config.ScatterIcon {
				scatterCount++
			}
		}

		// 计算赢奖
		wins, winColumns := m.calculateWins(currentGrid)
		var pagePayout int32
		for _, win := range wins {
			pagePayout += win.Value
		}

		// 计算每一列被移除的木条数量
		if len(spin.Cartimes) == 0 {
			spin.Cartimes = make([]int, m.Config.Column)
		}

		for i := 0; i < m.Config.Column; i++ {
			if _, ok := winColumns[i]; ok { //该列有中奖
				if spin.Carmuls[i] > 0 { //该列有翻倍符号
					spin.Cartimes[i]++
					if !spin.IsFreeModel && spin.Cartimes[i] > 3 { //普通模式只能移除3根木条
						spin.Cartimes[i] = 3
					} else if spin.IsFreeModel && spin.Carmuls[i] > 2 { //免费模式只能移除2根木条
						spin.Cartimes[i] = 2
					}
				}
			}
		}

		//spin.Cartimes保存最终每列被移除的木条数
		//page.Cartimes 保存每次消除后被移除的木条数
		pageCartimes := make([]int, m.Config.Column)
		copy(pageCartimes, spin.Cartimes)

		// 统计所有木条全被移除的翻倍数之和
		totalmul := 0
		for idx, v := range pageCartimes {
			if !spin.IsFreeModel && v >= 3 {
				totalmul += int(spin.Carmuls[idx])
			} else if spin.IsFreeModel && v >= 2 {
				totalmul += int(spin.Carmuls[idx])
			}
		}

		page := games.P10000006{
			Grid:         currentGrid,
			Pay:          pagePayout,
			Lines:        wins,
			ScatterCount: scatterCount,
			WinColumns:   winColumns,
			Cartimes:     pageCartimes,
			TotalMul:     totalmul,
		}

		if len(wins) > 0 {
			mobiles, newGrid := m.performCascade(currentGrid, wins, rd, spin)
			page.Mobiles = mobiles
			spin.Pages = append(spin.Pages, page)
			spin.Pays += pagePayout
			currentGrid = newGrid
		} else {
			spin.Pages = append(spin.Pages, page)
			if pagePayout > 0 {
				spin.Pays += pagePayout
			}
			break
		}
	}
}

// 计算赢奖
func (m *m10000006) calculateWins(grid []int32) ([]games.L10000006, map[int]struct{}) {
	var wins []games.L10000006
	winColumns := make(map[int]struct{})
	lineID := 1
	iconTimes := make(map[int32]int)       //记录每个图标的次数
	iconPositions := make(map[int32][]int) //记录每个图标的索引值

	for idx, n := range grid {
		if _, ok := iconTimes[n]; ok {
			iconTimes[n]++
		} else {
			iconTimes[n] = 1
		}

		if _, ok := iconPositions[n]; ok {
			iconPositions[n] = append(iconPositions[n], idx)
		} else {
			iconPositions[n] = []int{idx}
		}
	}

	for icon, cnt := range iconTimes {
		if icon != m.Config.ScatterIcon && cnt >= 8 {
			payouttable := m.Config.PayoutTable[icon]
			//赔付值
			itemPay := 0
			for i := len(payouttable) - 1; i >= 0; i-- {
				if cnt >= payouttable[i][0] && cnt <= payouttable[i][1] {
					itemPay = payouttable[i][2]
					break
				}
			}

			wins = append(wins, games.L10000006{
				ID:        lineID,
				Positions: iconPositions[icon], //该图标在grid中所有出现的索引[]int
				Icon:      int16(icon),         //中奖的图标
				Value:     int32(itemPay),      // 总赢奖
			})
			lineID++

			for _, v := range iconPositions[icon] {
				wincol := v % m.Config.Column
				winColumns[wincol] = struct{}{}
			}
		}
	}

	return wins, winColumns
}

func (m *m10000006) performCascade(grid []int32, wins []games.L10000006, rd *rand.Rand, spin *games.S10000006) ([]games.MobileColumn10000006, []int32) {
	//fmt.Println("消除前:")
	//for row := 0; row < m.Config.Row; row++ {
	//	for col := 0; col < m.Config.Column; col++ {
	//		idx := row*m.Config.Column + col
	//		fmt.Printf("%v\t", grid[idx])
	//	}
	//	fmt.Println()
	//}

	// 标记需要移除的格子索引
	toRemove := make(map[int]bool)
	for _, win := range wins {
		for _, pos := range win.Positions {
			toRemove[pos] = true
		}
		// test
		//fmt.Print("消除图标:", win.Icon, ",个数:", len(win.Positions), " 消除的索引:", win.Positions, " ")
		//fmt.Print("消除坐标:")
		//for _, n := range win.Positions {
		//	rowindex := n / m.Config.Column
		//	colindex := n % m.Config.Column
		//	fmt.Printf("(%d,%d),", rowindex, colindex)
		//}
		//fmt.Println()
	}

	newGrid := make([]int32, len(grid))
	mobiles := make([]games.MobileColumn10000006, m.Config.Column)
	//处理每列的下落逻辑
	for col := 0; col < m.Config.Column; col++ {
		colMobiles := []games.Mobile10000006{}
		remainingIcons := []int32{}     //收集没被消除的图标
		remainingPositions := [][]int{} //收集没被消除的坐标
		removedCount := 0               //被移除的数量

		// 收集该列中未被消除的格子
		for row := 0; row < m.Config.Row; row++ {
			pos := row*m.Config.Column + col
			if !toRemove[pos] {
				remainingIcons = append(remainingIcons, grid[pos])
				remainingPositions = append(remainingPositions, []int{row, col, pos})
			} else {
				removedCount++
			}
		}

		// 生成新图标用于填充被移除的格子
		var newIcons []int32
		if spin.IsFreeModel {
			newIcons = m.RandByFreeIconWeight.More(removedCount, rd)
		} else {
			newIcons = m.RandByWeight.More(removedCount, rd)
		}

		// 从底部向上填充新格子
		newIconIndex := 0
		remainingIndex := 0
		//currentRow := m.Config.Row - 1

		// 填充剩余的格子，从底部向上
		availableRows := make([]int, 0)
		for row := m.Config.Row - 1; row >= 0; row-- {
			availableRows = append(availableRows, row)
		}

		for _, row := range availableRows {
			targetPos := row*m.Config.Column + col
			//从下向上填充
			if remainingIndex < len(remainingIcons) {
				newGrid[targetPos] = remainingIcons[len(remainingIcons)-1-remainingIndex]
				beforeposition := remainingPositions[len(remainingIcons)-1-remainingIndex]

				if beforeposition[2] != targetPos {
					colMobiles = append(colMobiles, games.Mobile10000006{
						Card: int(newGrid[targetPos]),
						Row:  beforeposition[0] + 1,
						Col:  col + 1,
						Type: 1,
						Trow: row + 1,
						Tcol: col + 1,
					})
				}
				remainingIndex++
			} else if newIconIndex < len(newIcons) {
				newGrid[targetPos] = newIcons[newIconIndex]
				colMobiles = append(colMobiles, games.Mobile10000006{
					Card: int(newIcons[newIconIndex]),
					Row:  -1,
					Col:  -1,
					Type: 2,
					Trow: row + 1,
					Tcol: col + 1,
				})
				newIconIndex++
			}

			//mobiles = append(mobiles, games.MobileColumn10000006{
			//	Mobiles: colMobiles,
			//	Col:     col + 1,
			//})
			mobiles[col] = games.MobileColumn10000006{
				Mobiles: colMobiles,
				Col:     col + 1,
			}
		}

	}

	//fmt.Println("len(mobiles)::", len(mobiles))
	//for _, mobile := range mobiles {
	//	fmt.Printf("%+v\n", mobile)
	//}
	//
	//fmt.Println("消除后:")
	//for row := 0; row < m.Config.Row; row++ {
	//	for col := 0; col < m.Config.Column; col++ {
	//		idx := row*m.Config.Column + col
	//		fmt.Printf("%v\t", newGrid[idx])
	//	}
	//	fmt.Println()
	//}
	//fmt.Println("===================")

	return mobiles, newGrid
}

func (m *m10000006) Salting(spin basic.ISpin, salt *rand.Rand) basic.ISpin {
	r := spin.(*games.S10000006)
	r.GameId = m.ID()
	r.Line = m.Line()
	r.Row = m.Config.Row
	r.Column = m.Config.Column
	return r
}

func (m m10000006) Rule() string {
	b, _ := json.Marshal(m.Config)
	return string(b)
}

func (m m10000006) InputCoef(ctl int32) int32 {
	return 100
}

func (m m10000006) Line() int32 { // 线数
	return 20
}

func (m m10000006) Exception(code int32) string {
	return games.S10000006{}.Exception(code)
}

func (m m10000006) MinPayout(ctl int32) int32 {
	return 0
}
