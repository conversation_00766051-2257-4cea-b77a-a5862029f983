package modules

import (
	"encoding/json"
	"igameCommon/basic"
	"igameCommon/utils"
	"igameHttp/games"
	"math/rand"
)

type c10000002 struct {
	//GameID int // 游戏ID
	//RuleID    int   // 生成器规则ID
	Row           int // 网格行数
	Column        int // 网格列数
	MaxPayout     int32
	IconWeight    map[int32]int
	Pattern       [][]basic.Position
	WildIcon      int32 // wildId
	SpecialProb   int32
	MaxRetries    int
	PayoutTable   map[int32]int32
	PrizeProb     int32             //每个位置出现奖金符号的概率 %
	PrizeWeight   map[float64]int32 //奖金比例出现的权重
	FreeSpinCount int               //免费轮数
}

// Example General

var _ = Factory.reg(basic.NewGeneral[*m10000002])

type m10000002 struct {
	Config            c10000002
	RandByWeight      *utils.RandomWeightPicker[int32, int]
	RandByPrizeWeight *utils.RandomWeightPicker[int32, int]
}

// ZeroSpin 生成一个不中奖的旋转
func (m *m10000002) ZeroSpin(ctl int32, rd *rand.Rand) basic.ISpin {
	for {
		spin := m.genNorm(rd)
		pay := spin.Payout()
		if pay == 0 {
			return &spin
		}
	}
}

func (m *m10000002) Salting(spin0 basic.ISpin, salt *rand.Rand) basic.ISpin {
	spin := spin0.(*games.S10000002)
	spin.GameId = m.ID()
	spin.Line = m.Line()
	spin.Row = m.Config.Row
	spin.Column = m.Config.Column
	spin.Pattern = m.Config.Pattern
	return spin
}

func (m *m10000002) Init(config []byte) {
	m.Config = utils.ParseYAML[c10000002](config)
	m.RandByWeight = utils.NewRandomWeightPicker(m.Config.IconWeight)

	mp := make(map[int32]int)
	for k, v := range m.Config.PrizeWeight {
		mp[int32(k*100)] = int(v)
	}
	m.RandByPrizeWeight = utils.NewRandomWeightPicker(mp)

}

func (m10000002) ID() int32 {
	return 10000002
}

func (m m10000002) Line() int32 { // 线数
	return 10
}

func (m m10000002) ClientMode() int32 {
	return basic.EnumClientMode.ONE
}

func (m m10000002) Exception(code int32) string {
	return games.S10000002{}.Exception(code)
}

func (m *m10000002) Spin(rd *rand.Rand) basic.ISpin {
	var spin games.S10000002

	if rd.Int31n(100) < m.Config.SpecialProb {
		spin = m.genSpec(rd)
	} else {
		spin = m.genNorm(rd)
	}

	if spin.Pays > m.Config.MaxPayout {
		spin.Pays = -1
	}
	return &spin
}

func (m m10000002) genNorm(rd *rand.Rand) games.S10000002 {
	grid := m.RandByWeight.More(m.Config.Row*m.Config.Column, rd)

	for i := 0; i < len(grid); i++ {
		if i == len(grid)-1 || i == len(grid)-3 {
			grid[i] = 0
		} else {
			// 确定是否使用奖金符号
			if rd.Int31n(100) < m.Config.PrizeProb {
				grid[i] = m.RandByPrizeWeight.One(rd)
			}
		}
	}

	spin := games.S10000002{
		Rounds: []games.P10000002{
			{
				Pages: [][]int32{grid},
			},
		},
	}

	spin.Rounds[0].Pays, spin.Rounds[0].Lines, spin.Rounds[0].Tenfold, spin.Rounds[0].IsPrizeIcon = m.parsePayout(grid)

	spin.Pays = spin.Rounds[0].Pays
	//fmt.Println("spin.Pages:", spin.Rounds[0].Pages)
	return spin
}

func (m m10000002) genSpec(rd *rand.Rand) games.S10000002 {
	spin := games.S10000002{}

	for j := 0; j < m.Config.FreeSpinCount; j++ {
		grid := make([]int32, m.Config.Row*m.Config.Column)
		for i := 0; i < len(grid); i++ {
			if j == 0 || i == len(grid)-1 || i == len(grid)-3 {
				grid[i] = 0
			} else {
				// 确定是否使用奖金符号
				if rd.Int31n(100) < m.Config.PrizeProb {
					grid[i] = m.RandByPrizeWeight.One(rd)
				} else {
					grid[i] = 9 // 9表示不显示图片
				}
			}
		}
		pays, lines, tenfold, isPrizeIcon := m.parsePayout(grid)
		if pays > 0 {
			//fmt.Println("pays:", pays, ",lines:", lines, ",tenfold:", tenfold)
		}
		spin.Pays += pays
		spin.Rounds = append(spin.Rounds, games.P10000002{
			Pages:       [][]int32{grid},
			Lines:       lines,
			Tenfold:     tenfold,
			Pays:        pays,
			IsPrizeIcon: isPrizeIcon,
		})
	}

	//fmt.Println("spin.Pages:", spin.Rounds[0].Pages)

	return spin
}

func (m m10000002) parsePayout(grid []int32) (payout int32, lines [][]int32, tenfold bool, isPrizeIcon bool) {
	var flag uint64

	for idx, pos := range m.Config.Pattern {
		x := pos[0].Index(int32(m.Config.Column))
		y := pos[1].Index(int32(m.Config.Column))
		z := pos[2].Index(int32(m.Config.Column))
		if grid[x] < 9 && grid[y] < 9 && grid[z] < 9 { // 9表示无图片, >=50表示奖金符号
			ico, pay := m.checkLine(grid[x], grid[y], grid[z])
			if pay > 0 {
				// 图标数字,模式串中的索引,图标对应的赔率,图标1在grid的索引,图标2在grid的索引,图标3在grid的索引,
				lines = append(lines, []int32{
					ico, int32(idx), pay,
					int32(x), int32(y), int32(z),
				})
				payout += int32(pay)
				flag |= 1<<x | 1<<y | 1<<z
			}
		}
	}
	// 如果所有格子都至少参与过一次命中，则 tenfold
	//if flag == (1<<uint(m.Config.Row*m.Config.Column))-1 {
	//	tenfold = true
	//}

	// 当5个以上的奖金符号出现, 就奖励所有奖金符号的奖金
	var prizeCount = 0
	var prizeSum int32 = 0
	for _, v := range grid {
		if v >= 50 { // 在grid中大于50的表示奖金符号
			prizeCount++
			prizeSum += v
		}
	}
	if prizeCount >= 5 {
		//payout += prizeSum / 100
		payout += prizeSum
		isPrizeIcon = true
	}

	//fmt.Println("payout:", payout, ",lines:", lines)
	return
}

// 判断一条线是否命中：万能符号（WildIcon）可替任何符号  返回: 图标的数字, 图标的赔率
func (m m10000002) checkLine(a, b, c int32) (int32, int32) {
	cnt := map[int32]int{a: 1, b: 1, c: 1}
	delete(cnt, m.Config.WildIcon)
	// 全是万能
	if len(cnt) == 0 {
		return m.Config.WildIcon, m.Config.PayoutTable[m.Config.WildIcon]
	}
	// 只有一种非万能符号，万能补齐即可
	if len(cnt) == 1 {
		for k := range cnt {
			return k, m.Config.PayoutTable[k]
		}
	}
	// 其它情况不成线
	return -1, 0
}

func (m m10000002) Rule() string {
	b, _ := json.Marshal(m.Config)
	return string(b)
}

func (m10000002) InputCoef(int32) int32 {
	return 100
}

func (m m10000002) MinPayout(ctl int32) int32 {
	return 0
}
