package modules

import (
	"igameCommon/basic"
	"igameCommon/utils"
	"igameHttp/games"
	"math/rand"
)

var _ = Factory.reg(basic.NewGeneral[*m10000106])

type c10000106 struct {
	GameID            int // 游戏ID
	RuleID            int // 生成器规则ID
	Row               int // 网格行数
	Column            int // 网格列数
	MaxPayout         int32
	WildIcon          int16
	ScatterIcon       int16
	PayoutTable       map[int16][]int16
	NormIconWeight    map[int16]int
	FreeIconWeight    map[int16]int
	LargeSymbolWeight map[int16]int
	SymbolFrameWeight map[int16]int
	FreeSpin          FreeSpinConfig10000106 // Free spin config
	FreeSpinWeight    map[int16]int
	MinLimit          map[int32]struct {
		X     int32
		Limit map[string]int32
	}
}

type FreeSpinConfig10000106 struct {
	Icon     int16 // Scatter icon ID
	MinCount int   // Minimum scatters for free spins
	Count    int   // Base free spin count
}

type m10000106 struct {
	Config                       c10000106
	RandByWeight                 *utils.RandomWeightPicker[int16, int]
	RandByWeightFree             *utils.RandomWeightPicker[int16, int]
	RandByWeightNoWild           *utils.RandomWeightPicker[int16, int]
	RandByWeightFreeNoWild       *utils.RandomWeightPicker[int16, int]
	RandByWeightNoWildAndScatter *utils.RandomWeightPicker[int16, int]
	RandByWeightFreeSpin         *utils.RandomWeightPicker[int16, int]
	RandByWeightLarge            *utils.RandomWeightPicker[int16, int]
	RandByWeightFrame            *utils.RandomWeightPicker[int16, int]
}

func (m *m10000106) Init(config []byte) {
	m.Config = utils.ParseYAML[c10000106](config)
	m.RandByWeight = utils.NewRandomWeightPicker(m.Config.NormIconWeight)
	m.RandByWeightFree = utils.NewRandomWeightPicker(m.Config.FreeIconWeight)
	noWildWeight := make(map[int16]int)
	noWildAndScatterWeight := make(map[int16]int)
	for icon, weight := range m.Config.NormIconWeight {
		if icon != m.Config.WildIcon {
			noWildWeight[icon] = weight
		}
		if icon != m.Config.WildIcon && icon != m.Config.ScatterIcon {
			noWildAndScatterWeight[icon] = weight
		}
	}
	m.RandByWeightNoWild = utils.NewRandomWeightPicker(noWildWeight)
	m.RandByWeightNoWildAndScatter = utils.NewRandomWeightPicker(noWildAndScatterWeight)

	noWildWeightFree := make(map[int16]int)
	for icon, weight := range m.Config.FreeIconWeight {
		if icon != m.Config.WildIcon {
			noWildWeightFree[icon] = weight
		}
	}
	m.RandByWeightFreeNoWild = utils.NewRandomWeightPicker(noWildWeightFree)

	m.RandByWeightFreeSpin = utils.NewRandomWeightPicker(m.Config.FreeSpinWeight)
	m.RandByWeightLarge = utils.NewRandomWeightPicker(m.Config.LargeSymbolWeight)
	m.RandByWeightFrame = utils.NewRandomWeightPicker(m.Config.SymbolFrameWeight)
}

func (m10000106) ID() int32 {
	return 10000106
}

func (m m10000106) Line() int32 { // 线数
	return 20
}

func (m m10000106) ClientMode() int32 {
	return basic.EnumClientMode.ONE
}

func (m m10000106) Exception(code int32) string {
	return (&games.S10000106{}).Exception(code)
}

func (m *m10000106) genGrid(rd *rand.Rand, isFreeModel bool) []games.Cell {
	grid := make([]games.Cell, m.Config.Row*m.Config.Column)
	for row := 0; row < m.Config.Row; row++ {
		for col := 0; col < m.Config.Column; col++ {
			index := row*m.Config.Column + col
			if isFreeModel {
				if col == 0 || col == m.Config.Column-1 {
					grid[index] = games.Cell{Symbol: m.RandByWeightFreeNoWild.One(rd), Frame: 0}
				} else {
					grid[index] = games.Cell{Symbol: m.RandByWeightFree.One(rd), Frame: 0}
				}
			} else {
				if col == 0 {
					grid[index] = games.Cell{Symbol: m.RandByWeightNoWild.One(rd), Frame: 0}
				} else {
					grid[index] = games.Cell{Symbol: m.RandByWeight.One(rd), Frame: 0}
				}
			}
		}
	}

	// 随机选择 2-4 列，在卷轴 2-5 (索引 1-4) 生成 2-4 个垂直连续 Silver frame 格子
	numColsWithFrames := rd.Intn(3) + 2 // 随机 2-4 列
	availableCols := []int{1, 2, 3, 4}  // 卷轴 2-5
	rd.Shuffle(len(availableCols), func(i, j int) {
		availableCols[i], availableCols[j] = availableCols[j], availableCols[i]
	})

	for i := 0; i < numColsWithFrames; i++ {
		col := availableCols[i]
		// 随机选择连续长度 (2-4)
		length := int(m.RandByWeightLarge.One(rd))
		// 随机选择起始行 (确保在行范围内)
		maxStartRow := m.Config.Row - length
		if maxStartRow < 0 {
			maxStartRow = 0
		}
		startRow := rd.Intn(maxStartRow + 1)
		// 随机选择一个普通符号
		symbol := m.RandByWeightNoWildAndScatter.One(rd)
		frame := m.RandByWeightFrame.One(rd)
		// 填充垂直连续格子
		for r := startRow; r < startRow+length && r < m.Config.Row; r++ {
			index := r*m.Config.Column + col
			grid[index] = games.Cell{Symbol: symbol, Frame: frame}
		}
	}

	return grid
}

func (m *m10000106) Spin(rd *rand.Rand) basic.ISpin {
	grid := m.genGrid(rd, false)
	spin := &games.S10000106{
		GameId:       int32(m.ID()),
		CascadeCount: 0,
		Line:         m.Line(),
		Row:          m.Config.Row,
		Column:       m.Config.Column,
	}
	cascadeIndex := 0
	_ = m.parsePayout(grid, spin, rd, 0)
	spin.Rounds = append(spin.Rounds, spin)

	if spin.IsStartFree && spin.IsFreeModel {
		for {
			grid = m.genGrid(rd, true)
			freespin := &games.S10000106{
				GameId:       int32(m.ID()),
				CascadeCount: 0,
				Line:         m.Line(),
				Row:          m.Config.Row,
				Column:       m.Config.Column,
				Multiplier:   spin.Multiplier,
				FreeSpins:    0,
				IsFreeModel:  true,
			}
			cascadeIndex = m.parsePayout(grid, freespin, rd, cascadeIndex)
			spin.Pays += freespin.Pays
			spin.FreeSpins += freespin.FreeSpins
			spin.FreeSpins--
			spin.Rounds = append(spin.Rounds, freespin)

			if spin.FreeSpins == 0 {
				break
			}
		}
	}

	return spin
}

func (m *m10000106) parsePayout(grid []games.Cell, spin *games.S10000106, rd *rand.Rand, cascadeIndex int) int {
	currentGrid := make([]games.Cell, len(grid))
	copy(currentGrid, grid)

	for {
		wins := m.calculateWins(currentGrid, cascadeIndex, spin.Multiplier)
		var pagePayout int32
		for _, win := range wins {
			pagePayout += win.Value
		}

		scatterCount := 0
		for _, cell := range currentGrid {
			if cell.Symbol == m.Config.FreeSpin.Icon {
				scatterCount++
			}
		}

		page := games.P10000106{
			Grid:         currentGrid,
			Pay:          pagePayout,
			Lines:        wins,
			CascadeIndex: cascadeIndex,
			ScatterCount: scatterCount,
		}

		if len(wins) > 0 {
			mobiles, newGrid := m.performCascade(currentGrid, wins, rd, spin)
			page.Mobiles = mobiles
			spin.Pages = append(spin.Pages, page)
			spin.Pay += pagePayout
			spin.Pays += pagePayout
			currentGrid = newGrid
			cascadeIndex++
		} else {
			spin.Pages = append(spin.Pages, page)
			if pagePayout > 0 {
				spin.Pays += pagePayout
			}
			break
		}
	}
	spin.CascadeCount = len(spin.Pages)
	// 免费判断
	spin.Scatters = m.checkScatters(currentGrid, spin, rd)

	return cascadeIndex
}

func (m *m10000106) calculateWins(grid []games.Cell, cascadeIndex int, mul int32) []games.L10000106 {
	var wins []games.L10000106
	lineID := 1
	checkedIcons := make(map[int16]bool)

	for _, icon := range grid {
		if icon.Symbol == m.Config.FreeSpin.Icon || checkedIcons[icon.Symbol] {
			continue
		}
		checkedIcons[icon.Symbol] = true

		ways, positions, consecutiveCols := m.checkWaysForIcon(grid, icon.Symbol)
		if consecutiveCols >= 3 {
			payouts, exists := m.Config.PayoutTable[icon.Symbol]
			if exists && consecutiveCols-1 < len(payouts) {
				basePayout := payouts[consecutiveCols-1]
				multiplier := int32(cascadeIndex)*mul + 1
				totalPayout := int32(basePayout) * int32(ways) * multiplier
				wins = append(wins, games.L10000106{
					ID:        lineID,
					Positions: positions,
					Icon:      icon.Symbol,
					Count:     consecutiveCols,
					Value:     totalPayout,
					Ways:      ways,
				})
				lineID++
			}
		}
	}
	return wins
}

func (m *m10000106) checkWaysForIcon(grid []games.Cell, targetIcon int16) (int, []int, int) {
	ways := 1
	positions := []int{}
	consecutiveCols := 0

	for col := 0; col < m.Config.Column; col++ {
		colMatches := 0
		colPositions := []int{}
		for row := 0; row < m.Config.Row; row++ {
			pos := row*m.Config.Column + col
			icon := grid[pos]
			if icon.Symbol == targetIcon || (icon.Symbol == m.Config.WildIcon && targetIcon != m.Config.FreeSpin.Icon) {
				colMatches++
				colPositions = append(colPositions, pos)
			}
		}
		if colMatches > 0 {
			ways *= colMatches
			consecutiveCols++
			positions = append(positions, colPositions...)
		} else {
			break
		}
	}
	return ways, positions, consecutiveCols
}

func (m *m10000106) performCascade(grid []games.Cell, wins []games.L10000106, rd *rand.Rand, spin *games.S10000106) ([]games.MobileColumn10000106, []games.Cell) {
	// 标记需要移除的格子
	toRemove := make(map[int]bool)
	for _, win := range wins {
		for _, pos := range win.Positions {
			toRemove[pos] = true
		}
	}

	// 收集被消除的框架信息（仅银框或金框）
	type FrameData struct {
		Col       int
		StartRow  int
		Length    int
		FrameType int16
		Symbol    int16
	}
	var frameData []FrameData
	for col := 0; col < m.Config.Column; col++ {
		for row := 0; row < m.Config.Row; row++ {
			index := row*m.Config.Column + col
			if toRemove[index] && (grid[index].Frame == 1 || grid[index].Frame == 2) {
				startRow := row
				symbol := grid[index].Symbol
				frameType := grid[index].Frame
				length := 1
				r := row + 1
				for r < m.Config.Row {
					nextIndex := r*m.Config.Column + col
					if toRemove[nextIndex] && grid[nextIndex].Frame == frameType && grid[nextIndex].Symbol == symbol {
						length++
						r++
					} else {
						break
					}
				}
				frameData = append(frameData, FrameData{
					Col:       col,
					StartRow:  startRow,
					Length:    length,
					FrameType: frameType,
					Symbol:    symbol,
				})
				row = r - 1
			}
		}
	}

	newGrid := make([]games.Cell, len(grid))
	mobiles := make([]games.MobileColumn10000106, m.Config.Column)

	// 处理每列的下落逻辑
	for col := 0; col < m.Config.Column; col++ {
		colMobiles := []games.Mobile10000106{}
		remainingIcons := []games.Cell{}
		removedCount := 0
		var framesInColumn []FrameData

		// 收集该列中未被消除的格子
		for row := 0; row < m.Config.Row; row++ {
			pos := row*m.Config.Column + col
			if !toRemove[pos] {
				remainingIcons = append(remainingIcons, games.Cell{
					Symbol: grid[pos].Symbol,
					Frame:  grid[pos].Frame,
				})
			} else {
				removedCount++
			}
		}

		// 收集该列中被消除的框架
		for _, fd := range frameData {
			if fd.Col == col {
				framesInColumn = append(framesInColumn, fd)
			}
		}

		// 生成新图标用于填充被移除的格子
		var newIcons []games.Cell
		if spin.IsFreeModel {
			if col == 0 || col == m.Config.Column-1 {
				icons32 := m.RandByWeightFreeNoWild.More(removedCount, rd)
				newIcons = make([]games.Cell, len(icons32))
				for i, icon := range icons32 {
					newIcons[i] = games.Cell{Symbol: int16(icon), Frame: 0}
				}
			} else {
				icons32 := m.RandByWeightFree.More(removedCount, rd)
				newIcons = make([]games.Cell, len(icons32))
				for i, icon := range icons32 {
					newIcons[i] = games.Cell{Symbol: int16(icon), Frame: 0}
				}
			}
		} else {
			if col == 0 || col == m.Config.Column-1 {
				icons32 := m.RandByWeightNoWild.More(removedCount, rd)
				newIcons = make([]games.Cell, len(icons32))
				for i, icon := range icons32 {
					newIcons[i] = games.Cell{Symbol: int16(icon), Frame: 0}
				}
			} else {
				icons32 := m.RandByWeight.More(removedCount, rd)
				newIcons = make([]games.Cell, len(icons32))
				for i, icon := range icons32 {
					newIcons[i] = games.Cell{Symbol: int16(icon), Frame: 0}
				}
			}
		}

		// 从底部向上填充新格子，考虑框架的长度
		newIconIndex := 0
		remainingIndex := 0
		currentRow := m.Config.Row - 1

		// 预计算框架占用位置
		frameOccupied := make(map[int]bool)
		for _, fd := range framesInColumn {
			var newFrameType int16
			var newSymbol int16
			// 只有被消除的框架才改变类型
			if fd.FrameType == 1 { // 银框升级为金框
				newFrameType = 2
				newSymbol = m.RandByWeightNoWildAndScatter.One(rd)
			} else if fd.FrameType == 2 { // 金框转换为 Wild
				newFrameType = 0
				newSymbol = m.Config.WildIcon
			} else {
				newFrameType = fd.FrameType
				newSymbol = fd.Symbol
			}

			// 计算框架下方被消除的格子数量
			removedBelow := 0
			for row := fd.StartRow + fd.Length; row < m.Config.Row; row++ {
				if toRemove[row*m.Config.Column+col] {
					removedBelow++
				}
			}

			// 计算框架的新位置（基于下方被消除的格子数量）
			targetRow := fd.StartRow + removedBelow
			if targetRow+fd.Length > m.Config.Row {
				targetRow = m.Config.Row - fd.Length // 确保框架不超出网格
			}
			if targetRow < 0 {
				targetRow = 0
			}

			// 填充框架格子
			for r := targetRow; r < targetRow+fd.Length && r < m.Config.Row; r++ {
				if frameOccupied[r] {
					continue // 跳过已占用的位置
				}
				index := r*m.Config.Column + col
				newGrid[index] = games.Cell{Symbol: newSymbol, Frame: newFrameType}
				frameOccupied[r] = true
				colMobiles = append(colMobiles, games.Mobile10000106{
					Card:  int(newSymbol),
					Row:   fd.StartRow + 1,
					Col:   col + 1,
					Type:  1,
					Trow:  r + 1,
					Tcol:  col + 1,
					Frame: newFrameType,
				})
			}
			currentRow = utils.Min(currentRow, targetRow-1) // 更新当前行，防止格子填充到框架上方
		}

		// 填充剩余的格子，从底部向上
		availableRows := make([]int, 0, m.Config.Row)
		for row := m.Config.Row - 1; row >= 0; row-- {
			if !frameOccupied[row] {
				availableRows = append(availableRows, row)
			}
		}

		for _, row := range availableRows {
			targetPos := row*m.Config.Column + col
			if remainingIndex < len(remainingIcons) {
				newGrid[targetPos] = remainingIcons[len(remainingIcons)-1-remainingIndex]
				origRow := m.Config.Row - 1 - remainingIndex
				if origRow != row {
					colMobiles = append(colMobiles, games.Mobile10000106{
						Card:  int(newGrid[targetPos].Symbol),
						Row:   origRow + 1,
						Col:   col + 1,
						Type:  1,
						Trow:  row + 1,
						Tcol:  col + 1,
						Frame: newGrid[targetPos].Frame,
					})
				}
				remainingIndex++
			} else if newIconIndex < len(newIcons) {
				newGrid[targetPos] = newIcons[newIconIndex]
				colMobiles = append(colMobiles, games.Mobile10000106{
					Card:  int(newIcons[newIconIndex].Symbol),
					Row:   -1,
					Col:   -1,
					Type:  1,
					Trow:  row + 1,
					Tcol:  col + 1,
					Frame: 0,
				})
				newIconIndex++
			}
		}

		mobiles[col] = games.MobileColumn10000106{
			Mobiles: colMobiles,
			Col:     col + 1,
		}
	}

	return mobiles, newGrid
}

func (m *m10000106) checkScatters(grid []games.Cell, spin *games.S10000106, rd *rand.Rand) []map[string]any {
	scatterList := []map[string]any{}
	scatterCount := 0
	scatterPositions := []int{}

	for gridIndex, gridValue := range grid {
		if gridValue.Symbol == m.Config.FreeSpin.Icon {
			scatterCount++
			scatterPositions = append(scatterPositions, gridIndex)
		}
	}

	if scatterCount >= m.Config.FreeSpin.MinCount && !spin.IsStartFree {
		spin.IsStartFree = true
		spin.IsFreeModel = true
		spin.FreeSpins = int32(m.Config.FreeSpin.Count)
		spin.Multiplier = 1
		for _, gridIndex := range scatterPositions {
			var extraSpins int
			var extraMultiplier int
			reward := m.RandByWeightFreeSpin.One(rd)

			switch reward {
			case 1:
				extraSpins = 1
				extraMultiplier = 0
				spin.FreeSpins += int32(extraSpins)
			case 2:
				extraSpins = 2
				extraMultiplier = 0
				spin.FreeSpins += int32(extraSpins)
			case 3:
				extraSpins = 3
				extraMultiplier = 0
				spin.FreeSpins += int32(extraSpins)
			case 4:
				extraMultiplier = 1
				spin.Multiplier += int32(extraMultiplier)
			}

			row := gridIndex / m.Config.Column
			col := gridIndex % m.Config.Column
			scatterList = append(scatterList, map[string]any{
				"Point": map[string]any{
					"x": row + 1,
					"y": col + 1,
				},
				"Mul":   extraMultiplier,
				"Count": extraSpins,
			})
		}
	} else if spin.IsFreeModel {
		for _, gridIndex := range scatterPositions {
			spin.FreeSpins += 1 // Increment free spins for each Scatter
			row := gridIndex / m.Config.Column
			col := gridIndex % m.Config.Column
			scatterList = append(scatterList, map[string]any{
				"Point": map[string]any{
					"x": row,
					"y": col,
				},
				"Mul":   0,
				"Count": 1,
			})
		}
	} else {
		for _, gridIndex := range scatterPositions {
			row := gridIndex / m.Config.Column
			col := gridIndex % m.Config.Column
			scatterList = append(scatterList, map[string]any{
				"Point": map[string]any{
					"x": row + 1,
					"y": col + 1,
				},
				"Mul":   0,
				"Count": 0,
			})
		}
	}
	return scatterList
}

func (m *m10000106) ZeroSpin(ctl int32, rd *rand.Rand) basic.ISpin {
	for {
		spin := m.Spin(rand.New(rd))
		if spin.Payout() == 0 {
			return spin
		}
	}
}

func (m *m10000106) Salting(spin basic.ISpin, salt *rand.Rand) basic.ISpin {
	return spin
}

func (m10000106) Rule() string {
	return ""
}

func (m10000106) InputCoef(ctl int32) int32 {
	switch ctl {
	default:
		return 100
	case 1:
		return 10000
	}
}

// 不同的算法模式的最小赔率 单线
func (m m10000106) MinPayout(ctl int32) int32 {
	mode, ok := m.Config.MinLimit[ctl]
	if !ok {
		return 0
	}
	return mode.X * m.Line()
}
