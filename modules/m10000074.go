package modules

import (
	"encoding/json"
	"igameCommon/basic"
	"igameCommon/utils"
	"igameHttp/games"
	"math/rand"
	"sort"
)

var _ = Factory.reg(basic.NewGeneral[*m10000074])

// m10000074 提供 Ways 玩法的基本实现
type m10000074 struct {
	Config c10000074
}

// 定义老虎机的基本配置
type c10000074 struct {
	Row         int32                 // 行数
	Column      int32                 // 列数
	GoldWeight  int32                 // 金色麻将概率
	WildIcon    int32                 // wildId
	PayoutTable [][3]int32            // 赔率表 [符号ID, 连线数, 赔率*100]
	FreeSpin    FreeSpinConfig1000074 // 免费旋转配置
	BaseCoef    []int32               // 普通模式级联乘数
	FreeCoef    []int32               // 免费旋转模式级联乘数
	MaxPayout   int32                 // 最大派彩
	IconWeight  map[int32]int32       // 符号出现的权重
}

// FreeSpinConfig 免费旋转的规则
type FreeSpinConfig1000074 struct {
	Icon       int32 // 触发免费旋转的符号ID
	Number     int   // 触发免费旋转的最小符号数
	FirstCount int   // 首次免费旋转次数
	MoreCount  int   // 额外触发免费旋转的次数
}

// ID 返回游戏ID
func (s *m10000074) ID() int32 {
	return 10000074 // 可根据不同游戏修改
}

func (m *m10000074) Line() int32 {
	return 20
}

func (m m10000074) ClientMode() int32 {
	return basic.EnumClientMode.ONE
}

func (m m10000074) Exception(code int32) string {
	return (&games.S10000074{}).Exception(code)
}

// Init 解析 YAML 配置
func (s *m10000074) Init(config []byte) {
	s.Config = utils.ParseYAML[c10000074](config)
}

func (m m10000074) Rule() string {
	b, _ := json.Marshal(m.Config)
	return string(b)
}

func (m m10000074) InputCoef(ctl int32) int32 {
	return 100
}

func (m *m10000074) Salting(spin0 basic.ISpin, salt *rand.Rand) basic.ISpin {
	r := spin0.(*games.S10000074)
	r.GameId = m.ID()
	r.Line = m.Line()
	return r
}

// Spin 进行一次旋转
func (s *m10000074) Spin(rd *rand.Rand) basic.ISpin {
	page := s.generatePage(rd, false)
	totalPay := page.Pay
	if totalPay > s.Config.MaxPayout {
		return &games.S10000074{Pays: -1}
	}
	spin := &games.S10000074{
		SN:     0,
		Pays:   totalPay,
		Rounds: []games.WaysPage10000074{page},
	}
	// 判断是否触发免费旋转
	freeSpins := 0

	if count, ok := page.Pages[len(page.Pages)-1].Special[s.Config.FreeSpin.Icon]; ok && count >= s.Config.FreeSpin.Number {
		freeSpins = s.Config.FreeSpin.FirstCount
		freeSpins += s.Config.FreeSpin.MoreCount * (count - s.Config.FreeSpin.Number)
	}

	// 处理免费旋转
	for i := 0; i < freeSpins; i++ {
		page = s.generatePage(rd, true)
		totalPay = page.Pay
		if totalPay > s.Config.MaxPayout {
			return &games.S10000074{Pays: -1}
		}
		spin.Rounds = append(spin.Rounds, page)
		spin.Pays += totalPay
	}
	for _, p := range spin.Rounds {
		spin.SN += int32(len(p.Pages))
	}
	return spin
}

// generatePage 生成页面（支持普通和免费模式）
func (s *m10000074) generatePage(rd *rand.Rand, isFree bool) games.WaysPage10000074 {
	cols, rows := int(s.Config.Column), int(s.Config.Row)
	icons := utils.RandomFromWeighted(s.Config.IconWeight, cols*rows, rd)
	icons[5] = 0
	icons[29] = 0
	grid := make([][]int32, cols)
	isGolden := make([][]bool, cols) // 标记金色麻将
	for col := 0; col < cols; col++ {
		grid[col] = make([]int32, rows)
		isGolden[col] = make([]bool, rows)
		for row := 0; row < rows; row++ {
			grid[col][row] = icons[col*rows+row]
			// 第2~4列随机生成金色麻将（排除 Wild 和 Scatter）
			if col >= 1 && col <= 3 && grid[col][row] != s.Config.WildIcon && rd.Intn(100) < int(s.Config.GoldWeight) { //金色麻将概率
				isGolden[col][row] = true
			}
		}
	}
	if isFree {
		for col := 0; col < cols; col++ {
			for row := 0; row < rows; row++ {

				if grid[col][row] == s.Config.FreeSpin.Icon { //免费游戏中不要套免费了
					grid[col][row] = utils.RandomFromWeighted(s.Config.IconWeight, 1, rd)[0]
				}
				//处理免费模式有消除的情况，把第2列的图标变成金色
				isGolden[2][row] = true
			}
		}
	}
	cascadeCount := 0
	pages := games.WaysPage10000074{}
	newDrop := make([][]int32, cols)
	for {
		basicPays := int32(0)
		wins := s.calcWays(grid)

		special := s.countScatter(grid)
		for _, win := range wins {
			basicPays += win.WinAmount
		}
		coef := s.getCascadingMultiplier(isFree, cascadeCount)
		icons := make([]int32, 0, cols*rows)
		golds := []int32{} //金色icon
		for col := 0; col < cols; col++ {
			for row := 0; row < rows; row++ {
				icons = append(icons, grid[col][row])
				if isGolden[col][row] {
					golds = append(golds, int32(col*rows+row))
				}
			}
		}
		newDrop_ := make([][]int32, cols) // 新掉落的
		for col := 0; col < cols; col++ {
			newDrop_[col] = []int32{}
			for row := 0; row < len(newDrop[col]); row++ {
				newDrop_[col] = append(newDrop_[col], newDrop[col][row])
			}
		}
		page := games.Page10000074{BasicPays: basicPays, Special: special, Coef: coef, DropIco: icons, Golden: golds, Win: wins, NewDrop: newDrop_}
		pages.Pages = append(pages.Pages, page)
		pages.Pay += basicPays * coef
		if len(wins) == 0 {
			break
		}
		cascadeCount++
		// 进行消除和掉落
		// 消除中奖符号并处理金色麻将转化为Wild
		mobiless := s.applyWins(grid, isGolden, wins)
		grid, newDrop, isGolden = s.dropSymbols(grid, isGolden, rd, isFree)
		for col := 0; col < cols; col++ {
			for row := 0; row < 5; row++ { // 4 --> 5 修复下落计算时最上面一行出现时没有正确生成图标
				item := mobiless[col].Mobiles[row]
				mobiless[col].Mobiles[row].Card = int(grid[item.Tcol-1][item.Trow])
			}
		}
		pages.Pages[len(pages.Pages)-1].Mobiless = mobiless
	}
	return pages
}

// applyWins 处理中奖符号的消除和金色麻将转换
func (s *m10000074) applyWins(grid [][]int32, golden [][]bool, wins []games.Win10000074) []games.Mobiless10000074 {
	cols := len(grid)
	rows := len(grid[0])
	// 标记需要移除的符号位置
	toRemove := make([][]bool, cols)
	for col := range grid {
		toRemove[col] = make([]bool, rows)
	}
	for _, win := range wins {
		for _, pos := range win.Positions {
			col, row := pos.Col, pos.Row
			if toRemove[col][row] {
				continue // 已经标记移除则跳过
			}
			if golden[col][row] && grid[col][row] != s.Config.WildIcon {
				// 金色麻将符号参与中奖：转化为Wild而不移除
				grid[col][row] = s.Config.WildIcon
				golden[col][row] = false
			} else {
				// 普通中奖符号或Wild：标记为移除
				toRemove[col][row] = true
			}
		}
	}
	mobiless := make([]games.Mobiless10000074, cols)
	// 移除标记的位置（设为-1表示空位）
	for col := 0; col < cols; col++ {
		mobiles := []games.Mobiles10000074{}
		for row := 0; row < rows; row++ {
			b := false
			if toRemove[col][row] {
				grid[col][row] = -1
				golden[col][row] = false
				// if !golden[col][row] {
				for i := 0; i < len(mobiles); i++ {
					mobiles[i].Trow = mobiles[i].Trow + 1
				}
				b = true
				// }
			}
			if row > 0 && row < 6 { // 5--->6 应计算1-5行
				item := games.Mobiles10000074{
					Card: int(grid[col][row]),
					Row:  row,     //掉落之前的位置
					Col:  col + 1, //掉落之前的位置
					Type: 1,
					Trow: row,     //掉落之后的位置
					Tcol: col + 1, //掉落之后的位置
				}
				if b {
					item.Trow = 1
					item.Row = -1
					item.Col = -1
				}
				mobiles = append(mobiles, item)
			}
		}
		// for i := 0; i < len(mobiles); i++ {
		// 	if mobiles[i].Trow > 4 {
		// 		mobiles[i].Trow = mobiles[i].Trow - 4
		// 		mobiles[i].Row = -1
		// 		mobiles[i].Col = -1
		// 	}
		// }
		sort.Slice(mobiles, func(i, j int) bool {
			return mobiles[i].Trow > mobiles[j].Trow
		})
		mobiless[col] = games.Mobiless10000074{
			Mobiles: mobiles,
			Col:     col + 1,
		}
	}
	return mobiless
}

// calcWays 计算 Ways 中奖
func (s *m10000074) calcWays(grid [][]int32) []games.Win10000074 {
	var wins []games.Win10000074
	for symbol := int32(1); symbol < 10; symbol++ { //9和10不连线
		ways := 1
		var positions []games.Position10000074
		line := 0
		for col := 0; col < len(grid); col++ {
			count := 0                     //这一列图标数量
			for row := 1; row < 6; row++ { // 仅索引为第1~5行参与连线,5--->6 修复中奖计算图标没有计算第5行
				if grid[col][row] == symbol || grid[col][row] == s.Config.WildIcon { // Wild 可替代
					count++
					positions = append(positions, games.Position10000074{Col: col, Row: row})
				}
			}
			if count > 0 {
				line++
				ways *= count
				// } else if col > 0 {
			} else {
				break // 断开则停止
			}
		}
		// if len(positions) >= 3 {
		if line >= 3 {
			winAmount := s.getPayout(symbol, line)
			winAmount = winAmount * int32(ways)
			wins = append(wins, games.Win10000074{Symbol: symbol, Ways: ways, WinAmount: winAmount, Positions: positions})
		}
	}
	return wins
}

// getPayout 获取赔率
func (s *m10000074) getPayout(symbol int32, count int) int32 {
	for _, payout := range s.Config.PayoutTable {
		if payout[0] == symbol && int(payout[1]) == count {
			return payout[2]
		}
	}
	return 0
}

// getCascadingMultiplier 获取级联乘数
func (s *m10000074) getCascadingMultiplier(isFree bool, cascadeCount int) int32 {
	if isFree {
		if cascadeCount < len(s.Config.FreeCoef) {
			return s.Config.FreeCoef[cascadeCount]
		}
		return s.Config.FreeCoef[len(s.Config.FreeCoef)-1]
	}
	if cascadeCount < len(s.Config.BaseCoef) {
		return s.Config.BaseCoef[cascadeCount]
	}
	return s.Config.BaseCoef[len(s.Config.BaseCoef)-1]
}

// countScatter 统计Scatter数量
func (s *m10000074) countScatter(grid [][]int32) map[int32]int {
	special := make(map[int32]int)
	for col := range grid {
		for row := range grid[col] {
			if grid[col][row] == s.Config.FreeSpin.Icon && row > 0 && row < 6 {
				special[s.Config.FreeSpin.Icon]++
			}
		}
	}
	return special
}

// dropSymbols 将空位由上方掉落符号填充，并生成新符号补充顶部
func (s *m10000074) dropSymbols(grid [][]int32, golden [][]bool, rd *rand.Rand, isFree bool) ([][]int32, [][]int32, [][]bool) {
	cols := len(grid)
	rows := len(grid[0])
	newDrop := make([][]int32, cols) // 新掉落的
	for col := 0; col < cols; col++ {
		newDrop[col] = []int32{}
		newCol := make([]int32, rows)
		newGold := make([]bool, rows)
		// 从底部往上填充符号
		dest := rows - 1
		for row := rows - 1; row >= 0; row-- {
			if grid[col][row] != -1 {
				newCol[dest] = grid[col][row]
				newGold[dest] = golden[col][row]
				dest--
			}
		}
		// 前方剩余位置用新随机符号填充
		if dest >= 0 {
			newCount := dest + 1
			newIcons := utils.RandomFromWeighted(s.Config.IconWeight, newCount, rd)
			for fill := 0; fill < newCount; fill++ {
				newCol[fill] = newIcons[fill]
				newDrop[col] = append(newDrop[col], newIcons[fill])
				newGold[fill] = false
				// 新生成符号如果在第2~4列且非特殊符号，则有概率为金色
				if col >= 1 && col <= 3 && !isFree {
					sym := newCol[fill]
					if sym != 0 && sym != s.Config.FreeSpin.Icon {
						if rd.Intn(100) < int(s.Config.GoldWeight) { //金色麻将概率
							newGold[fill] = true
						}
					}
				} else {
					if col == 2 && isFree {
						newGold[fill] = true
					}
				}
			}
		}
		grid[col] = newCol
		golden[col] = newGold
	}
	return grid, newDrop, golden
}

// ZeroSpin 生成一个不中奖的旋转
func (s *m10000074) ZeroSpin(ctl int32, rd *rand.Rand) basic.ISpin {
	for {
		spin := s.Spin(rand.New(rd))
		pay := spin.Payout()
		if pay == 0 {
			return spin
		}
	}
}

func (m m10000074) MinPayout(ctl int32) int32 {
	return 0
}
