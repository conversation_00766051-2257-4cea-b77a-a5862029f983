package modules

import (
	"encoding/json"
	"igameCommon/basic"
	"igameCommon/utils"
	"igameHttp/games"
	"math/rand"
)

var _ = Factory.reg(basic.NewGeneral[*m11695365])

type c11695365 struct {
	MaxPayout             int32 // 最大派彩
	SpecialProb           int32
	Row                   int   // 行数
	Column                int   // 列数
	WildIcon              int16 // wildId
	FreeSpins             int   // 免费旋转次数
	Pattern               [][]basic.Position
	PayoutTable           map[int16]int16
	IconWeight            map[int16]int // 图标权重
	MultiplierWeight      map[int16]int // 倍数权重
	MultiplierTimesWeight map[int16]int // 出现 2,3 次倍数权重
}

type m11695365 struct {
	Config              c11695365
	RandByWeight        *utils.RandomWeightPicker[int16, int]
	MultiplierPicker    *utils.RandomWeightPicker[int16, int]
	MultiplierNo1Picker *utils.RandomWeightPicker[int16, int]
	MultiplierTimes     *utils.RandomWeightPicker[int16, int]
}

func (m *m11695365) Init(config []byte) {
	m.Config = utils.ParseYAML[c11695365](config)
	m.RandByWeight = utils.NewRandomWeightPicker(m.Config.IconWeight)
	m.MultiplierPicker = utils.NewRandomWeightPicker(m.Config.MultiplierWeight)

	tempWeight := make(map[int16]int)
	for k, v := range m.Config.MultiplierWeight {
		if k != 1 {
			tempWeight[k] = v
		}
	}
	m.MultiplierNo1Picker = utils.NewRandomWeightPicker(tempWeight)
	m.MultiplierTimes = utils.NewRandomWeightPicker(m.Config.MultiplierTimesWeight)
}

func (m11695365) ID() int32 {
	return 11695365
}

func (m m11695365) Line() int32 { // 线数
	return 5
}

func (m m11695365) ClientMode() int32 {
	return basic.EnumClientMode.ONE
}

func (m m11695365) Exception(code int32) string {
	return games.S11695365{}.Exception(code)
}

func (m *m11695365) Spin(rd *rand.Rand) basic.ISpin {
	var spin games.S11695365

	if rd.Int31n(100) < m.Config.SpecialProb {
		spin = m.genSpec(rd)
	} else {
		spin = m.genNorm(rd)
	}
	return &spin
}

func (m m11695365) genNorm(rd *rand.Rand) games.S11695365 {
	grid := m.RandByWeight.More(m.Config.Row*m.Config.Column, rd)

	onecol := []int16{0, 0, 0}
	// 避免两边出现 1 倍率
	onecol[0] = m.MultiplierNo1Picker.One(rd)
	onecol[1] = m.MultiplierPicker.One(rd)
	onecol[2] = m.MultiplierNo1Picker.One(rd)

	pays, lines, _ := m.parsePayout(grid, onecol[1])
	spin := games.S11695365{
		Pays: pays,
		SN:   0,
		Rounds: []games.P11695356{
			{
				Pays:       pays,
				Pages:      [][]int16{grid},
				Lines:      lines,
				Tenfold:    false,
				Onecol:     onecol,
				Multiplier: onecol[1],
			},
		},
	}
	return spin
}

func (m m11695365) genSpec(rd *rand.Rand) games.S11695365 {
	var spin games.S11695365

	tempWeight := make(map[int16]int)
	for k, v := range m.Config.MultiplierWeight {
		// 0 倍率不参与权重计算
		if k != 1 {
			tempWeight[k] = v
		}
	}
	tempPicker := utils.NewRandomWeightPicker(tempWeight)

	// 特殊模式次数默认+1, 例如免费 8 次,实际一共转 9 次
	for i := 0; i < m.Config.FreeSpins+1; i++ {
		// 生成网格
		grid := m.RandByWeight.More(m.Config.Row*m.Config.Column, rd)
		// 特殊模式不会出现 0 倍率
		// 特殊模式倍率通过权重获取 2,3 次倍数
		onecol := tempPicker.More(2+int(m.MultiplierTimes.One(rd)), rd)
		// 计算总倍率
		var multiplier int16
		for _, mul := range onecol[1 : len(onecol)-1] {
			multiplier += mul
		}
		// 计算 payout
		payout, lines, tf := m.parsePayout(grid, multiplier)
		spin.Pays += payout

		spin.Rounds = append(spin.Rounds, games.P11695356{
			Pays:        payout,
			Pages:       [][]int16{grid},
			Lines:       lines,
			Tenfold:     tf,
			Onecol:      onecol,
			Multiplier:  multiplier,
			FreeWinGold: payout * int32(multiplier),
		})
	}

	return spin
}

func (m m11695365) parsePayout(grid []int16, multiplier int16) (payout int32, lines [][]int16, tenfold bool) {
	var flag uint64
	for idx, pos := range m.Config.Pattern {
		x := pos[0].Index(int32(m.Config.Column))
		y := pos[1].Index(int32(m.Config.Column))
		z := pos[2].Index(int32(m.Config.Column))
		ico, pay := m.checkLine(grid[x], grid[y], grid[z])
		if pay > 0 {
			lines = append(lines, []int16{
				ico, int16(idx), pay,
				int16(x), int16(y), int16(z),
			})
			payout += int32(pay) * int32(multiplier)
			flag |= 1<<x | 1<<y | 1<<z
		}
	}
	if flag == (1<<uint(m.Config.Row*m.Config.Column))-1 {
		tenfold = true
	}
	return
}

func (m m11695365) checkLine(a, b, c int16) (int16, int16) {
	cnt := map[int16]int{a: 1, b: 1, c: 1}
	delete(cnt, m.Config.WildIcon)
	if len(cnt) == 0 {
		return m.Config.WildIcon, m.Config.PayoutTable[m.Config.WildIcon]
	}
	if len(cnt) == 1 {
		for k := range cnt {
			return k, m.Config.PayoutTable[k]
		}
	}
	return -1, 0
}

// ZeroSpin 生成一个不中奖的旋转
func (s *m11695365) ZeroSpin(ctl int32, rd *rand.Rand) basic.ISpin {
	for {
		spin := s.Spin(rand.New(rd))
		pay := spin.Payout()
		if pay == 0 {
			return spin
		}
	}
}

func (m *m11695365) Salting(spin basic.ISpin, salt *rand.Rand) basic.ISpin {
	r := spin.(*games.S11695365)
	r.GameId = m.ID()
	r.Line = m.Line()
	r.Row = m.Config.Row
	r.Column = m.Config.Column
	r.Pattern = m.Config.Pattern
	return r
}

func (m m11695365) Rule() string {
	b, _ := json.Marshal(m.Config)
	return string(b)
}

func (m11695365) InputCoef(int32) int32 {
	return 100
}

func (m m11695365) MinPayout(ctl int32) int32 {
	return 0
}
