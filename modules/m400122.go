package modules

import (
	"encoding/json"
	"fmt"
	"igameCommon/basic"
	"igameCommon/utils"
	"igameHttp/games"
	"math/rand"
)

// 闪电配置
type LightConfig struct {
	Probability int         //出现闪电的概率
	Weights     map[int]int //两种闪电的权重
}

type c400122 struct {
	Row            int // 网格行数
	Column         int // 网格列数
	MaxPayout      int //最大反奖倍数
	ScatterIcon    int //Scatter图标
	IconWeight     map[int]int
	FreeIconWeight map[int]int
	PayoutTable    map[int][]int //反奖率对照表
	FreeSpinTimes  []int         //免费游戏次数
	MaxFreeSpin    int           // 最大免费游戏次数
	CoinMap        map[int]int   //硬币图标编号与硬币面额映射
	// 闪电配置
	LightConfigs struct {
		Normal LightConfig
		Free   LightConfig
	}
	MummyProbability int //出现木乃伊的概率
	HotProb          int
	MinLimit         map[int32]struct {
		X     int32
		Limit map[string]int32
	}
}

// Example General
var _ = Factory.reg(basic.NewGeneral[*m400122])

type m400122 struct {
	Config                      c400122
	RandByIconWeight            *utils.RandomWeightPicker[int, int]
	RandByFreeIconWeight        *utils.RandomWeightPicker[int, int] //免费模式的图标
	RandByFreeIconNoCoinsWeight *utils.RandomWeightPicker[int, int] //免费模式除开硬币符号的图标
	RandByLightWeight           *utils.RandomWeightPicker[int, int] //闪电
	RandByFreeLightWeight       *utils.RandomWeightPicker[int, int] //免费模式闪电
}

func (m *m400122) Init(config []byte) {
	m.Config = utils.ParseYAML[c400122](config)
	m.RandByIconWeight = utils.NewRandomWeightPicker(m.Config.IconWeight)
	// 免费模式所有的图标
	m.RandByFreeIconWeight = utils.NewRandomWeightPicker(m.Config.FreeIconWeight)

	//免费模式除开硬币符号的图标
	mp := make(map[int]int)
	for k, v := range m.Config.FreeIconWeight {
		if k >= 12 && k <= 19 {
			//硬币图标
		} else {
			mp[k] = v
		}
	}
	m.RandByFreeIconNoCoinsWeight = utils.NewRandomWeightPicker(mp)

	m.RandByLightWeight = utils.NewRandomWeightPicker(m.Config.LightConfigs.Normal.Weights)
	m.RandByFreeLightWeight = utils.NewRandomWeightPicker(m.Config.LightConfigs.Free.Weights)
}

func (m400122) ID() int32 {
	return 400122
}

func (m m400122) Line() int32 { // 线数
	return 20
}

func (m m400122) ClientMode() int32 {
	return basic.EnumClientMode.ONE
}

func (m m400122) Exception(code int32) string {
	return games.S400122{}.Exception(code)
}

func (m *m400122) genNormGrid(rd *rand.Rand) (stopBox [][]int, startBox [][]int, grid [][]int, maskStones [][][]int, maxIndexes []int, light *games.Light) {
	// 设置stopBox
	stopBox = make([][]int, 0)
	for i := 0; i < m.Config.Row; i++ {
		arr := make([]int, m.Config.Column)
		for j := 0; j < m.Config.Column; j++ {
			arr[j] = 9
		}
		stopBox = append(stopBox, arr)
	}

	for i := 0; i < 10*m.Config.Row; i++ {
		var arr = m.RandByIconWeight.More(m.Config.Column, rd)
		stopBox = append(stopBox, arr)
	}

	// 设置StartBox
	startBox = make([][]int, m.Config.Row)
	for i := 0; i < m.Config.Row; i++ {
		startBox[i] = m.RandByIconWeight.More(m.Config.Column, rd)
	}

	// test
	//stopBox = m.getTestStopBox()

	// 设置grid
	grid = make([][]int, 0)
	curidx := len(stopBox) - 3
	for i := 0; i < m.Config.Row; i++ {
		if i >= 2 && i <= 4 {
			tmp := make([]int, m.Config.Column)
			copy(tmp, stopBox[curidx])
			grid = append(grid, tmp)
			curidx++
		} else {
			// 一整行石头 用-9表示
			tmp := make([]int, m.Config.Column)
			for j, _ := range tmp {
				tmp[j] = -9
			}
			grid = append(grid, tmp)
		}
	}

	// 设置maskStones: 石头用1表示 其他用0表示
	maskStones = make([][][]int, 0)
	firstmask := make([][]int, 0)
	for i := 0; i < m.Config.Row; i++ {
		tmp := make([]int, 0)
		for j := 0; j < m.Config.Column; j++ {
			if i >= 2 && i <= 4 {
				tmp = append(tmp, 0)
			} else {
				tmp = append(tmp, 1)
			}
		}
		firstmask = append(firstmask, tmp)
	}
	maskStones = append(maskStones, firstmask)

	// 设置maxIndexes 每一列图标当前在stopBox中的最大的索引
	maxIndexes = make([]int, m.Config.Column)
	for i := 0; i < len(maxIndexes); i++ {
		maxIndexes[i] = len(stopBox) - 3
	}

	// 设置闪电
	// 如果grid中存在Scatter符号 ,则直接不设置闪电
	flag := true
outer:
	for _, arr := range grid {
		for _, v := range arr {
			if v == m.Config.ScatterIcon {
				flag = false
				break outer
			}
		}
	}

	var isLight bool // 是否设置闪电
	if !flag {
		isLight = false //如果grid中存在Scatter符号 ,则直接不设置闪电
	} else { //如果grid中没有Scatter符号, 根据概率来决定是否设置闪电
		n := rd.Intn(100)
		//if isFreeModel && n < m.Config.LightConfigs.Free.Probability {
		//	isLight = true
		//} else if !isFreeModel && n < m.Config.LightConfigs.Normal.Probability {
		//	isLight = true
		//} else {
		//	isLight = false
		//}

		if n < m.Config.LightConfigs.Normal.Probability {
			isLight = true
		} else {
			isLight = false
		}

		if isLight {
			light = new(games.Light)
			light.Type = m.RandByLightWeight.One(rd)
			light.LightRow = rd.Intn(3) + 2
			light.LightReel = rd.Intn(m.Config.Column)
			light.Winmask = make([][]int, m.Config.Row)
			for i := 0; i < len(light.Winmask); i++ {
				light.Winmask[i] = make([]int, m.Config.Column)
			}

			if light.Type == games.BLUE { //蓝色闪电
				smashStoneNum := rd.Intn(3) + 3               // 打碎3-5个石头
				topSmashNum := rd.Intn(2) + 2                 //上方打碎2-3个
				bottomSmashNum := smashStoneNum - topSmashNum //下方打碎石头个数
				smashed := make(map[string]struct{})          //标记已经被打碎的位置
				for ; topSmashNum > 0; topSmashNum-- {
					randrow := 1 // 只能打碎最近的一行石头 不能隔行打
					randcol := rd.Intn(m.Config.Column)
					key := fmt.Sprintf("%d,%d", randrow, randcol)
					if _, ok := smashed[key]; !ok {
						smashed[key] = struct{}{}
						light.Winmask[randrow][randcol] = 1
					}
				}
				for ; bottomSmashNum > 0; bottomSmashNum-- {
					randrow := 5 // 只能打碎最近的一行石头 不能隔行打
					randcol := rd.Intn(m.Config.Column)
					key := fmt.Sprintf("%d,%d", randrow, randcol)
					if _, ok := smashed[key]; !ok {
						smashed[key] = struct{}{}
						light.Winmask[randrow][randcol] = 1
					}
				}
				//还需要将闪电所在位置进行标记
				light.Winmask[light.LightRow][light.LightReel] = 1

				// test
				//light.Type = games.BLUE
				//light.LightRow = 4
				//light.LightReel = 6
				//light.Winmask[1][0] = 1
				//light.Winmask[4][6] = 1
				//light.Winmask[5][2] = 1
				//light.Winmask[5][6] = 1

			} else if light.Type == games.YELLOW { //黄色闪电
				//test
				//light.LightRow = 3
				//light.LightReel = 6

				// 黄色闪电 消除棋盘上的低等符号(4-7) , 并且闪电消除的符号不会引起石头爆炸
				for i := 0; i < len(grid); i++ {
					for j := 0; j < len(grid[i]); j++ {
						icon := grid[i][j]
						if icon >= 4 && icon <= 7 {
							light.Winmask[i][j] = 1
						}
					}
				}
				// 闪电自身位置也标记为1
				light.Winmask[light.LightRow][light.LightReel] = 1
			}
		}
	}

	return
}

func (m *m400122) genFreeGrid(rd *rand.Rand, prevStopBox [][]int, prevMaskStones [][]int, isHot bool) (stopBox [][]int, startBox [][]int, grid [][]int, maskStones [][][]int, maxIndexes []int, light *games.Light) {
	// 生成StartBox
	startBox = make([][]int, 0)
	if len(prevStopBox) == 0 {
		for i := 0; i < m.Config.Row; i++ {
			arr := m.RandByIconWeight.More(m.Config.Column, rd)
			startBox = append(startBox, arr)
		}
	} else {
		for _, rows := range prevStopBox[len(prevStopBox)-7:] {
			var tmp []int
			copy(tmp, rows)
			startBox = append(startBox, tmp)
		}
	}

	// 设置stopBox
	stopBox = make([][]int, 0)
	for i := 0; i < m.Config.Row; i++ {
		arr := make([]int, m.Config.Column)
		for j := 0; j < m.Config.Column; j++ {
			arr[j] = 9
		}
		stopBox = append(stopBox, arr)
	}

	coinnums := 0 //记录硬币数量 免费模式的stopBox不要超过3个硬币
	for i := 0; i < 10*m.Config.Row; i++ {
		//var arr = m.RandByFreeIconWeight.More(m.Config.Column, rd)
		var arr = make([]int, m.Config.Column)
		for j := 0; j < len(arr); j++ {
			var icon int
			if coinnums < 3 {
				icon = m.RandByFreeIconWeight.One(rd)
				if icon >= 12 && icon <= 19 {
					coinnums++
				}
			} else {
				icon = m.RandByFreeIconNoCoinsWeight.One(rd)
				//icon = m.RandByFreeIconWeight.One(rd)
			}
			arr[j] = icon
		}
		stopBox = append(stopBox, arr)
	}

	// 设置石头标记  石头用1表示 其他用0表示
	maskStones = make([][][]int, 0)
	if len(prevMaskStones) == 0 {
		firstmask := make([][]int, 0)
		for i := 0; i < m.Config.Row; i++ {
			tmp := make([]int, 0)
			for j := 0; j < m.Config.Column; j++ {
				if i >= 2 && i <= 4 {
					tmp = append(tmp, 0)
				} else {
					tmp = append(tmp, 1)
				}
			}
			firstmask = append(firstmask, tmp)
		}
		maskStones = append(maskStones, firstmask)
	} else {
		// 将上一轮最后的石头标记 作为本轮开始的石头标记
		firstmask := make([][]int, m.Config.Row)
		for i := 0; i < m.Config.Row; i++ {
			firstmask[i] = make([]int, m.Config.Column)
		}
		for i := 0; i < len(prevMaskStones); i++ {
			for j := 0; j < len(prevMaskStones[i]); j++ {
				firstmask[i][j] = prevMaskStones[i][j]
			}
		}
		maskStones = append(maskStones, firstmask)
	}

	if isHot {
		for i := 0; i < len(maskStones); i++ {
			for j := 0; j < len(maskStones[i]); j++ {
				for k := 0; k < len(maskStones[i][j]); k++ {
					maskStones[i][j][k] = 0
				}
			}
		}
	}

	// 设置grid
	grid = make([][]int, m.Config.Row)
	for i := 0; i < len(grid); i++ {
		grid[i] = make([]int, m.Config.Column)
		//for j := 0; j < len(grid[i]); j++ {
		//	grid[i][j] = -9
		//}
	}
	// 设置maxIndexes 每一列图标当前在stopBox中的最大的索引
	maxIndexes = make([]int, m.Config.Column)
	//for i := 0; i < len(maxIndexes); i++ {
	//	maxIndexes[i] = len(stopBox) - 3
	//}
	for col := 0; col < m.Config.Column; col++ {
		endidx := len(stopBox) - 1
		for row := m.Config.Row - 1; row >= 0; row-- {
			if maskStones[0][row][col] == 1 {
				grid[row][col] = -9
			} else {
				grid[row][col] = stopBox[endidx][col]
				maxIndexes[col] = endidx
				endidx--
			}
		}
	}

	// 设置闪电
	// 如果grid中存在Scatter符号 ,则直接不设置闪电
	flag := true
outer:
	for _, arr := range grid {
		for _, v := range arr {
			if v == m.Config.ScatterIcon {
				flag = false
				break outer
			}
		}
	}

	var isLight bool // 是否设置闪电
	if !flag {
		isLight = false //如果grid中存在Scatter符号 ,则直接不设置闪电
	} else { //如果grid中没有Scatter符号, 根据概率来决定是否设置闪电
		n := rd.Intn(100)
		//if isFreeModel && n < m.Config.LightConfigs.Free.Probability {
		//	isLight = true
		//} else if !isFreeModel && n < m.Config.LightConfigs.Normal.Probability {
		//	isLight = true
		//} else {
		//	isLight = false
		//}

		if n < m.Config.LightConfigs.Free.Probability {
			isLight = true
		} else {
			isLight = false
		}

		if isLight {
			light = new(games.Light)
			light.Type = m.RandByFreeLightWeight.One(rd)
			light.LightRow = rd.Intn(3) + 2
			light.LightReel = rd.Intn(m.Config.Column)
			light.Winmask = make([][]int, m.Config.Row)
			for i := 0; i < len(light.Winmask); i++ {
				light.Winmask[i] = make([]int, m.Config.Column)
			}

			if light.Type == games.BLUE { //蓝色闪电
				// 收集可以打碎的石头位置,不能隔行打碎
				tmpPos := make([][]int, 0)
				for col := 0; col < m.Config.Column; col++ {
					for row := 0; row < m.Config.Row; row++ {
						if m.canBlueLightSmash(grid, []int{row, col}) && !(row == light.LightRow && col == light.LightReel) {
							tmpPos = append(tmpPos, []int{row, col})
						}
					}
				}
				utils.Shuffle(tmpPos)
				// 打碎石头数量 3-5个
				smashStoneNum := rd.Intn(3) + 3 // 打碎3-5个石头
				if smashStoneNum > len(tmpPos) {
					smashStoneNum = len(tmpPos)
				}
				for _, p := range tmpPos {
					light.Winmask[p[0]][p[1]] = 1
				}
				//还需要将闪电所在位置进行标记
				light.Winmask[light.LightRow][light.LightReel] = 1

			} else if light.Type == games.YELLOW { //黄色闪电
				//test
				//light.LightRow = 3
				//light.LightReel = 6

				// 黄色闪电 消除棋盘上的低等符号(4-7) , 并且闪电消除的符号不会引起石头爆炸
				for i := 0; i < len(grid); i++ {
					for j := 0; j < len(grid[i]); j++ {
						icon := grid[i][j]
						if icon >= 4 && icon <= 7 {
							light.Winmask[i][j] = 1
						}
					}
				}
				// 闪电自身位置也标记为1
				light.Winmask[light.LightRow][light.LightReel] = 1
			}
		}
	}

	return
}

func (m *m400122) Spin(rd *rand.Rand) basic.ISpin {
	var (
		stopBox    [][]int
		startBox   [][]int
		grid       [][]int
		maskStones [][][]int
		maxIndexes []int
		light      *games.Light
		isHot      bool
	)

	if rd.Intn(100) < m.Config.HotProb {
		isHot = true
	}

	if !isHot {
		stopBox, startBox, grid, maskStones, maxIndexes, light = m.genNormGrid(rd)
	} else {
		stopBox, startBox, grid, maskStones, maxIndexes, light = m.genFreeGrid(rd, nil, nil, true)
	}

	spin := &games.S400122{
		GameId:      m.ID(),
		Line:        m.Line(),
		Row:         m.Config.Row,
		Column:      m.Config.Column,
		StartBox:    startBox,
		StopBox:     stopBox,
		MaskStones:  maskStones,
		MaxIndexes:  maxIndexes,
		IsFreeModel: false,
		IsStartFree: false,
		IsHot:       isHot,
		IsStartHot:  isHot,
		SMult:       1, //开始的翻倍数
		FMult:       1, //结束的翻倍数
	}

	m.parsePayout(stopBox, grid, spin, rd, light)
	if isHot {
		spin.IsStartFree = true
		spin.IsFreeModel = true
	}
	spin.Rounds = append(spin.Rounds, spin)

	scatterCount := 0
	for _, page := range spin.Pages {
		scatterCount += len(page.Scatters)
	}
	if isHot {
		scatterCount = 3
	}

	// 免费游戏
	var freetimes int = m.calculateFreeTimes(scatterCount)

	if freetimes > 0 {

		spin.IsStartFree = true
		spin.FreeSpins = int32(freetimes)
		spin.FreeInfo = games.FreeInfo{
			Total:      freetimes,
			Remain:     freetimes,
			Award:      freetimes,
			Nesting:    1,
			WildDir:    utils.IfElse(spin.IsHot, 1, 0),
			WildReelID: scatterCount,
			AllWin:     0,
			FGAux:      1,
		}

		prevStopBox := make([][]int, 0)    //上一轮的StopBox
		prevMaskStones := make([][]int, 0) //上一轮的石头标记
		var smult int = scatterCount       //开始的翻倍数
		var fmult int = scatterCount       //结束的翻倍数
		var freeWin int32 = 0
		freeWin = 0

		totalfreetimes := freetimes

		for ; freetimes > 0; freetimes-- {
			stopBox, startBox, grid, maskStones, maxIndexes, light := m.genFreeGrid(rd, prevStopBox, prevMaskStones, isHot)
			freespin := &games.S400122{
				GameId:      m.ID(),
				Line:        m.Line(),
				Row:         m.Config.Row,
				Column:      m.Config.Column,
				StartBox:    startBox,
				StopBox:     stopBox,
				MaskStones:  maskStones,
				MaxIndexes:  maxIndexes,
				IsFreeModel: true,
				IsHot:       isHot,
				IsStartHot:  false,
				IsStartFree: false,
				SMult:       smult, //开始的翻倍数
				FMult:       fmult, //结束的翻倍数
			}
			addBugs, addMummy := m.parsePayout(stopBox, grid, freespin, rd, light)
			freespin.FMult = freespin.FMult + addBugs + addMummy

			//spin.Pays += freespin.Pays
			spin.Rounds = append(spin.Rounds, freespin)

			prevStopBox = freespin.StopBox
			prevMaskStones = freespin.MaskStones[len(freespin.MaskStones)-1]
			smult = freespin.FMult
			fmult = freespin.FMult
			freeWin += freespin.Pays

			freespin.FreeInfo = games.FreeInfo{
				Total:      int(spin.FreeSpins),
				Remain:     freetimes - 1,
				Award:      0,
				Nesting:    1,
				WildDir:    utils.IfElse(spin.IsHot, 1, 0),
				WildReelID: spin.FreeInfo.WildReelID,
				AllWin:     int(freeWin),
				FGAux:      0,
			}

			// 石头全部打碎 再增加3次
			if spin.IsStartFree && !isHot {
				// 剩余石头个数
				stoneCount := 0
				lastgrid := freespin.Pages[len(freespin.Pages)-1].Grid
				for _, rows := range lastgrid {
					for _, v := range rows {
						if v == -9 {
							stoneCount++
						}
					}
				}
				if stoneCount == 0 {
					//fmt.Println("石头全打碎了....")
					if totalfreetimes+3 <= m.Config.MaxFreeSpin {
						totalfreetimes += 3
						freetimes += 3
						freespin.FreeInfo.Total += 3
						freespin.FreeInfo.Remain += 3
					}
				}
			}

		}
	}

	return spin
}

func (m *m400122) parsePayout(stopBox [][]int, grid [][]int, spin *games.S400122, rd *rand.Rand, light *games.Light) (addBugs int, addMummy int) {
	currentGrid := make([][]int, 0)
	for i, _ := range grid {
		tmp := make([]int, len(grid[i]))
		copy(tmp, grid[i])
		currentGrid = append(currentGrid, tmp)
	}

	pageIndex := 0

	for ; ; pageIndex++ {
		// 计算赢奖
		lineInfoList, lineInfoV2List, hasWin := m.calculateWins(currentGrid, light, pageIndex)
		if spin.IsStartHot {
			lineInfoList = nil
			lineInfoV2List = nil
			hasWin = false
			addBugs = 3
			addMummy = 0
			return
		}

		//根据grid给Page上分配Scatter撞击
		scatters := m.setPageScatters(currentGrid, rd)
		if spin.IsFreeModel {
			addBugs += len(scatters) //scatter图标增加的额外翻倍
		}
		// 统计硬币
		coins := m.getCoins(currentGrid)

		var pagePayout int32
		for _, v := range lineInfoV2List {
			pagePayout += int32(v.Win)
		}

		// 额外的木乃伊翻倍
		mummyMult := 0
		if spin.IsFreeModel {
			if rd.Intn(100) < m.Config.MummyProbability {
				mummyMult = rd.Intn(6) + 3
				addMummy += mummyMult //累计的额外木乃伊翻倍
			}
		}

		page := games.P400122{
			Pay:            pagePayout,
			Grid:           currentGrid,
			LineInfoList:   lineInfoList,
			LineInfoV2List: lineInfoV2List,
			Coins:          coins,     //硬币列表
			Scatters:       scatters,  //scatter列表
			MummyMult:      mummyMult, // 额外木乃伊翻倍
		}
		if light != nil && pageIndex == 0 {
			page.Lights = []*games.Light{light}
		} else {
			page.Lights = nil
		}
		spin.Pages = append(spin.Pages, page)
		spin.Pays += pagePayout

		if hasWin {
			newGrid := m.performCascade(currentGrid, lineInfoV2List, rd, spin, light, pageIndex, scatters)
			currentGrid = newGrid
		} else if pageIndex == 0 && light != nil && light.Type == games.BLUE {
			// 执行蓝色闪电
			newGrid := m.performBlueLight(currentGrid, spin, light, scatters)
			currentGrid = newGrid
		} else if pageIndex == 0 && light != nil && light.Type == games.YELLOW {
			// 执行黄色闪电
			newGrid := m.performYellowLight(currentGrid, spin, light, scatters)
			currentGrid = newGrid
		} else {
			break
		}

	}

	// 硬币的计算
	lastpage := spin.Pages[len(spin.Pages)-1]
	lastcoins := lastpage.Coins
	// 翻倍
	if spin.IsFreeModel {
		// 最新的翻倍数
		newfmult := spin.FMult + addBugs + addMummy
		if newfmult > 0 {
			//fmt.Println("spin.Pays:", spin.Pays, "spin.FMult:", spin.FMult)
			spin.Pays = spin.Pays * int32(newfmult)
		}
	}

	if len(lastcoins) >= 4 {
		if !spin.IsFreeModel { //普通模式的硬币计算
			coinsum := 0
			for _, coin := range lastcoins {
				coinsum += coin.Value
			}
			originpays := spin.Pays
			spin.Pays = int32(coinsum)*m.Line() + originpays
		} else {
			// 免费模式的硬币的计算
			coinsum := 0
			for _, coin := range lastcoins {
				coinsum += coin.Value
			}

			newfmult := spin.FMult + addBugs + addMummy

			originpays := spin.Pays
			spin.Pays = originpays + int32(coinsum)*m.Line()*int32(newfmult)
		}
	}

	return
}

// 执行蓝色闪电
func (m *m400122) performBlueLight(grid [][]int, spin *games.S400122, light *games.Light, scatters []*games.Scatter) (newGrid [][]int) {
	// 设置初始的newGrid 假设所有位置全部是石头 -9
	newGrid = make([][]int, m.Config.Row)
	for i := 0; i < len(newGrid); i++ {
		newGrid[i] = make([]int, m.Config.Column)
		for j := 0; j < len(newGrid[i]); j++ {
			newGrid[i][j] = -9
		}
	}

	// 记录需要爆炸的石头的索引  key:rowIndex,colIndex
	boomIndexes := make(map[string]bool)
	for i := 0; i < len(light.Winmask); i++ {
		for j := 0; j < len(light.Winmask[i]); j++ {
			if light.Winmask[i][j] == 1 {
				boomIndexes[fmt.Sprintf("%d,%d", i, j)] = true
			}
		}
	}

	// 处理每列的下落
	for col := 0; col < m.Config.Column; col++ {
		highrow := 4 //本列非石头的最大行索引
		lowrow := 2  //本列非石头的最小行索引
		for i := 4; i < m.Config.Row; i++ {
			if grid[i][col] != -9 {
				highrow = i
			} else {
				// 判断该石头是否被爆炸
				if light.Winmask[i][col] == 1 {
					highrow = i
				} else {
					break
				}
			}
		}
		for i := 2; i >= 0; i-- {
			if grid[i][col] != -9 {
				lowrow = i
			} else {
				//判断该石头是否被爆炸
				if light.Winmask[i][col] == 1 {
					lowrow = i
				} else {
					break
				}
			}
		}

		// 收集这一列原本剩下的图标坐标
		remainingPositions := make([][]int, 0)
		for row := 0; row < m.Config.Row; row++ {
			// 如果这个位置是石头 直接跳过
			if grid[row][col] == -9 {
				continue
			}
			// 如果这个位置不是闪电 才进行收集
			if !(row == light.LightRow && col == light.LightReel) {
				remainingPositions = append(remainingPositions, []int{row, col})
			}
		}

		// 先将剩余的图标掉落下来
		currentrow := highrow
		for i := len(remainingPositions) - 1; i >= 0; i-- {
			newGrid[currentrow][col] = grid[remainingPositions[i][0]][remainingPositions[i][1]]
			currentrow--
		}

		// 需要的新图标的个数
		newIconNum := highrow - lowrow + 1 - len(remainingPositions)
		maxIdxInStopBox := spin.MaxIndexes[col]
		spin.MaxIndexes[col] -= newIconNum
		for i := maxIdxInStopBox - 1; i >= maxIdxInStopBox-newIconNum && i >= 0; i-- {
			newGrid[currentrow][col] = spin.StopBox[i][col]
			currentrow--
		}
	}

	// 追加一组新的石头标记
	newMaskStones := make([][]int, 0)
	for i := 0; i < m.Config.Row; i++ {
		tmp := make([]int, m.Config.Column)
		for j := 0; j < m.Config.Column; j++ {
			if newGrid[i][j] == -9 {
				tmp[j] = 1
			} else {
				tmp[j] = 0
			}
		}
		newMaskStones = append(newMaskStones, tmp)
	}
	spin.MaskStones = append(spin.MaskStones, newMaskStones)

	return
}

// 执行黄色闪电
func (m *m400122) performYellowLight(grid [][]int, spin *games.S400122, light *games.Light, scatters []*games.Scatter) (newGrid [][]int) {
	// 设置初始的newGrid 假设所有位置全部是石头 -9
	newGrid = make([][]int, m.Config.Row)
	for i := 0; i < len(newGrid); i++ {
		newGrid[i] = make([]int, m.Config.Column)
		for j := 0; j < len(newGrid[i]); j++ {
			newGrid[i][j] = -9
		}
	}

	// 记录需要消除的位置的索引  key:rowIndex,colIndex
	toremoves := make(map[string]bool)
	for i := 0; i < len(light.Winmask); i++ {
		for j := 0; j < len(light.Winmask[i]); j++ {
			if light.Winmask[i][j] == 1 {
				toremoves[fmt.Sprintf("%d,%d", i, j)] = true
			}
		}
	}

	//处理每一列的下落
	for col := 0; col < m.Config.Column; col++ {
		// 最大行索引和最小行索引不变化 因为黄色闪电消除图标不会炸开石头
		highrow := 4 //本列非石头的最大行索引
		lowrow := 2  //本列非石头的最小行索引

		for i := 4; i < m.Config.Row; i++ {
			if grid[i][col] != -9 {
				highrow = i
			} else {
				break
			}
		}
		for i := 2; i >= 0; i-- {
			if grid[i][col] != -9 {
				lowrow = i
			} else {
				break
			}
		}

		//收集这一列原本剩下的图标坐标
		remainingPositions := make([][]int, 0)
		for row := 0; row < m.Config.Row; row++ {
			// 如果这个位置是石头 直接跳过
			if grid[row][col] == -9 {
				continue
			}
			isLight := row == light.LightRow && col == light.LightReel
			key := fmt.Sprintf("%d,%d", row, col)
			_, isRemove := toremoves[key]
			if !isLight && !isRemove {
				remainingPositions = append(remainingPositions, []int{row, col})
			}
		}

		// 先将剩余的图标掉落下来
		currentrow := highrow
		for i := len(remainingPositions) - 1; i >= 0; i-- {
			newGrid[currentrow][col] = grid[remainingPositions[i][0]][remainingPositions[i][1]]
			currentrow--
		}
		// 需要的新图标的个数
		newIconNum := highrow - lowrow + 1 - len(remainingPositions)
		maxIdxInStopBox := spin.MaxIndexes[col]
		spin.MaxIndexes[col] -= newIconNum
		for i := maxIdxInStopBox - 1; i >= maxIdxInStopBox-newIconNum && i >= 0; i-- {
			newGrid[currentrow][col] = spin.StopBox[i][col]
			currentrow--
		}
	}

	// 追加一组新的石头标记
	newMaskStones := make([][]int, 0)
	for i := 0; i < m.Config.Row; i++ {
		tmp := make([]int, m.Config.Column)
		for j := 0; j < m.Config.Column; j++ {
			if newGrid[i][j] == -9 {
				tmp[j] = 1
			} else {
				tmp[j] = 0
			}
		}
		newMaskStones = append(newMaskStones, tmp)
	}
	spin.MaskStones = append(spin.MaskStones, newMaskStones)

	return
}

func (m *m400122) performCascade(grid [][]int, lineinfov2list []games.LineInfoV2, rd *rand.Rand, spin *games.S400122, light *games.Light, pageIndex int, scatters []*games.Scatter) (newGrid [][]int) {
	// 设置初始的newGrid 假设所有位置全部是石头 -9
	newGrid = make([][]int, m.Config.Row)
	for i := 0; i < len(newGrid); i++ {
		newGrid[i] = make([]int, m.Config.Column)
		for j := 0; j < len(newGrid[i]); j++ {
			newGrid[i][j] = -9
		}
	}

	// 标记需要移除的格子索引, key:rowIndex,colIndex
	toRemove := make(map[string]bool)
	for _, infoV2 := range lineinfov2list {
		for _, p := range infoV2.Positions {
			key := fmt.Sprintf("%d,%d", p[0], p[1])
			toRemove[key] = true
		}
	}

	// Scatter撞击引起的移除 也收集
	scatterToRemove := make(map[string]bool)
	for _, scatter := range scatters {
		for _, rmp := range scatter.ToRemovePositions {
			key := fmt.Sprintf("%d,%d", rmp[0], rmp[1])
			toRemove[key] = true
			scatterToRemove[key] = true
		}
	}

	//fmt.Println("toRemove::::", toRemove)

	//处理每列下落
	for col := 0; col < m.Config.Column; col++ {
		highrow := 4 //本列非石头的最大行索引
		lowrow := 2  //本列非石头的最小行索引
		for i := 4; i < m.Config.Row; i++ {
			if grid[i][col] != -9 {
				highrow = i
			} else {
				break
			}
		}
		for i := 2; i >= 0; i-- {
			if grid[i][col] != -9 {
				lowrow = i
			} else {
				break
			}
		}

		//fmt.Println("highrow:::", highrow)
		//fmt.Println("lowrow:::", lowrow)

		// 收集这一列未被消除的图标坐标
		remainingPositions := make([][]int, 0)
		for row := 0; row < m.Config.Row; row++ {
			key := fmt.Sprintf("%d,%d", row, col)

			// 如果这个位置原本是石头 直接跳过
			if grid[row][col] == -9 {
				continue
			}

			if !toRemove[key] {
				remainingPositions = append(remainingPositions, []int{row, col})
			} else {
				// 该位置被消除了,但不是scatter撞击引起的消除 , 判断是否需要爆炸上下方的石头
				if !scatterToRemove[key] {
					if row == highrow && row != m.Config.Row-1 {
						highrow += 1
					}
					if row == lowrow && row != 0 {
						lowrow -= 1
					}
				}
			}
		}

		// Scatter撞击也会造成最大和最小行索引变化
		for _, scatter := range scatters {
			for _, rmp := range scatter.ToRemovePositions {
				rowindex := rmp[0]
				colindex := rmp[1]
				if colindex == col {
					if rowindex < lowrow {
						lowrow = rowindex
					}
					if rowindex > highrow {
						highrow = rowindex
					}
				}
			}
		}

		//fmt.Println("col", col, "highrow:::", highrow)
		//fmt.Println("col", col, "lowrow:::", lowrow)

		// 先将剩余的图标掉落下来
		currentrow := highrow
		for i := len(remainingPositions) - 1; i >= 0; i-- {
			newGrid[currentrow][col] = grid[remainingPositions[i][0]][remainingPositions[i][1]]
			currentrow--
		}

		// 需要的新图标的个数
		newIconNum := highrow - lowrow + 1 - len(remainingPositions)
		//fmt.Println("col", col, "需要的新图标的个数:::", newIconNum, "剩余数:", remainingPositions)
		maxIdxInStopBox := spin.MaxIndexes[col]
		spin.MaxIndexes[col] -= newIconNum
		for i := maxIdxInStopBox - 1; i >= maxIdxInStopBox-newIconNum && i >= 0; i-- {
			newGrid[currentrow][col] = spin.StopBox[i][col]
			currentrow--
		}
	}

	//fmt.Println("newgrid::::")
	//for _, v := range newGrid {
	//	fmt.Println(v)
	//}

	// 追加一组新的石头标记
	newMaskStones := make([][]int, 0)
	for i := 0; i < m.Config.Row; i++ {
		tmp := make([]int, m.Config.Column)
		for j := 0; j < m.Config.Column; j++ {
			if newGrid[i][j] == -9 {
				tmp[j] = 1
			} else {
				tmp[j] = 0
			}
		}
		newMaskStones = append(newMaskStones, tmp)
	}
	spin.MaskStones = append(spin.MaskStones, newMaskStones)

	return
}

// 计算赢奖
func (m *m400122) calculateWins(grid [][]int, light *games.Light, pageIndex int) (lineInfoList []games.LineInfo, lineInfoV2List []games.LineInfoV2, hasWin bool) {
	//如果有闪电 也产生一个新的page 但是没有赢奖
	if pageIndex == 0 {
		if light != nil {
			lineInfoList = append(lineInfoList, games.LineInfo{
				Win:       0,
				WinWomult: 0,
				Mult:      1,
			})
			lineInfoV2List = make([]games.LineInfoV2, 0)
			hasWin = false
			return
		}
	}

	checkedIcons := make(map[int]bool) //记录已经被检查过的图标
	tmpLineInfoList := make([]games.LineInfo, 0)

	for row := 0; row < m.Config.Row; row++ {
		for col := 0; col < m.Config.Column; col++ {
			icon := grid[row][col]
			// 跳过夺宝图标,石头,硬币符号 以及被检查过的图标
			if icon == m.Config.ScatterIcon ||
				icon == -9 ||
				icon >= 12 ||
				checkedIcons[icon] {
				continue
			}
			checkedIcons[icon] = true

			ways, positions, consecutiveCols := m.checkWaysForIcon(grid, icon)
			//fmt.Printf("icon:%v ways:%v positions:%v consecutiveCols:%v\n", icon, ways, positions, consecutiveCols)

			if consecutiveCols >= 3 {
				if payouts, exists := m.Config.PayoutTable[icon]; exists {
					basePayout := payouts[consecutiveCols]
					totalPayout := basePayout * ways
					lineinfoV2 := games.LineInfoV2{
						PicId:     icon,
						Win:       totalPayout,
						N:         consecutiveCols,
						Positions: positions,
					}
					lineInfoV2List = append(lineInfoV2List, lineinfoV2)

					// 一个page内可产生多个lineinfo
					//winmask := m.getWinmask(grid, positions)
					//lineinfo := games.LineInfo{
					//	Win:       totalPayout,
					//	WinWomult: totalPayout, // ???
					//	Mult:      1,           //???
					//	Winmask:   winmask,
					//}
					//lineInfoList = append(lineInfoList, lineinfo)

					// 一个page内只产生1个lineInfo, 先保存到临时数组tmpLineInfoList中
					winmask := m.getWinmask(grid, positions)
					lineinfo := games.LineInfo{
						Win:       totalPayout,
						WinWomult: totalPayout, // ???
						Mult:      1,           //???
						Winmask:   winmask,
					}
					tmpLineInfoList = append(tmpLineInfoList, lineinfo)

					hasWin = true
				}
			}
		}
	}

	if hasWin {
		lineinfoSum := games.LineInfo{}
		for _, info := range tmpLineInfoList {
			lineinfoSum.Win += info.Win
			lineinfoSum.WinWomult += info.WinWomult
			lineinfoSum.Mult = 1
		}
		lineinfoSum.Winmask = make([][]int, m.Config.Row)
		for i := 0; i < m.Config.Row; i++ {
			lineinfoSum.Winmask[i] = make([]int, m.Config.Column)
		}
		for i := 0; i < m.Config.Row; i++ {
			for j := 0; j < m.Config.Column; j++ {
				for _, info := range tmpLineInfoList {
					if info.Winmask[i][j] == 1 {
						lineinfoSum.Winmask[i][j] = 1
						break
					}
				}
			}
		}
		lineInfoList = append(lineInfoList, lineinfoSum)
	}

	// 如果没有中奖 linesInfo追加一个默认对象
	if !hasWin {
		lineInfoList = append(lineInfoList, games.LineInfo{
			Win:       0,
			WinWomult: 0,
			Mult:      1,
		})
		lineInfoV2List = make([]games.LineInfoV2, 0)

		// 统计grid中的Scatter, 如果存在, hasWin 设置为true,触发后面的消除
		flag := false
	outer:
		for _, arr := range grid {
			for _, v := range arr {
				if v == m.Config.ScatterIcon {
					flag = true
					break outer
				}
			}
		}
		if flag {
			hasWin = true
		} else {
			hasWin = false
		}

	}

	return
}

// 根据Scatter图标数量 计算免费游戏次数
func (m *m400122) calculateFreeTimes(scatterCount int) (freetimes int) {
	lastidx := len(m.Config.FreeSpinTimes) - 1
	if scatterCount > lastidx {
		return m.Config.FreeSpinTimes[lastidx]
	}

	for i := len(m.Config.FreeSpinTimes); i >= 0; i-- {
		if i == scatterCount {
			return m.Config.FreeSpinTimes[i]
		}
	}

	return
}

func (m *m400122) getWinmask(grid [][]int, winPositions [][]int) (winmask [][]int) {
	// 初始化winmask 设置所有位置为为0
	for i := 0; i < m.Config.Row; i++ {
		tmp := make([]int, m.Config.Column)
		for j := 0; j < m.Config.Column; j++ {
			tmp[j] = 0
		}
		winmask = append(winmask, tmp)
	}

	// 将grid拷贝到临时的tempGrid中 避免操作影响原数组
	tempGrid := make([][]int, len(grid))
	for i := 0; i < len(grid); i++ {
		tempGrid[i] = make([]int, m.Config.Column)
		for j := 0; j < len(tempGrid[i]); j++ {
			tempGrid[i][j] = grid[i][j]
		}
	}

	//计算最底部的行索引
	bottomrow := 0
	// 保存每一列的最底部一行的行索引
	highrows := make([]int, m.Config.Column)
	for col := 0; col < m.Config.Column; col++ {
		for row := m.Config.Row - 1; row >= 0; row-- {
			icon := grid[row][col]
			if icon != -9 {
				highrows[col] = row
				if highrows[col] > bottomrow {
					bottomrow = highrows[col]
				}
				break
			}
		}
	}
	//fmt.Println("bottomrow::", bottomrow)
	//fmt.Println("highrows::", highrows)

	// 将tempGrid的每一列底部对齐
	rowdiffs := make([]int, m.Config.Column)
	for col := 0; col < m.Config.Column; col++ {
		lastrowidx := highrows[col]
		if lastrowidx == bottomrow {
			// 已经对齐
			continue
		}

		diff := bottomrow - lastrowidx
		for row := m.Config.Row - 1; row >= 0; row-- {
			value := tempGrid[row][col]
			newrow := row + diff
			if newrow >= m.Config.Row {
				continue
			}
			tempGrid[newrow][col] = value
		}
		rowdiffs[col] = diff
	}
	//fmt.Println("tempGrid:::")
	//for _, v := range tempGrid {
	//	for _, vv := range v {
	//		fmt.Printf("%v\t", vv)
	//	}
	//	fmt.Println()
	//}

	//将winmask的最后一行和tempGrid的bottomrow行对齐
	diffrows := len(winmask) - 1 - bottomrow
	for _, wp := range winPositions {
		row := wp[0] + diffrows + rowdiffs[wp[1]]
		col := wp[1]
		winmask[row][col] = 1
	}

	////fmt.Println("lastrowindex:", lastrowindex)
	//diffrows := len(winmask) - 1 - lastrowindex
	//
	//for _, wp := range winPositions {
	//	row := wp[0] + diffrows
	//	col := wp[1]
	//	winmask[row][col] = 1
	//}
	//
	////fmt.Println("winmask:")
	////for _, wm := range winmask {
	////	fmt.Println(wm)
	////}

	return
}

// 根据grid给Page上分配Scatter撞击
func (m *m400122) setPageScatters(grid [][]int, rd *rand.Rand) []*games.Scatter {
	scatters := make([]*games.Scatter, 0)
	// 优先遍历列
	for col := 0; col < m.Config.Column; col++ {
		for row := 0; row < m.Config.Row; row++ {
			icon := grid[row][col]
			if icon == m.Config.ScatterIcon {
				var sctype int
				//如果scatter图标 在边框上,就只能水平或垂直撞击 .撞击方向: 0向四周撞击  1垂直撞击 2水平撞击
				if row == 0 || col == 0 || row == m.Config.Row-1 || col == m.Config.Column-1 {
					sctype = rd.Intn(2) + 1
				} else { //水平,垂直,或四周撞击
					sctype = rd.Intn(3)
				}

				// 撞击将会引起移除的格子坐标列表
				toRemovePositions := make([][]int, 0)
				if sctype == 0 {
					// 移除四周八个格子 和自己
					for a := row - 1; a <= row+1; a++ {
						for b := col - 1; b <= col+1; b++ {
							if grid[a][b] >= 12 && grid[a][b] <= 19 {
								// 硬币不消除
							} else if grid[a][b] == m.Config.ScatterIcon && !(a == row && b == col) {
								// 范围内的其他Scatter符号 不消除
							} else {
								toRemovePositions = append(toRemovePositions, []int{a, b})
							}
						}
					}
				} else if sctype == 1 { //垂直撞击
					for a := 0; a < m.Config.Row; a++ {
						if grid[a][col] >= 12 && grid[a][col] <= 19 {
							// 硬币不消除
						} else if grid[a][col] == m.Config.ScatterIcon && a != row {
							// 范围内的其他Scatter符号 不消除
						} else {
							toRemovePositions = append(toRemovePositions, []int{a, col})
						}
					}
				} else if sctype == 2 { //水平撞击
					for b := 0; b < m.Config.Column; b++ {
						if grid[row][b] >= 12 && grid[row][b] <= 19 {
							//硬币不消除
						} else if grid[row][b] == m.Config.ScatterIcon && b != col {
							// 范围内的其他Scatter符号 不消除
						} else {
							toRemovePositions = append(toRemovePositions, []int{row, b})
						}
					}
				}
				scatter := &games.Scatter{
					Position:          []int{row, col},
					ScType:            sctype,
					ToRemovePositions: toRemovePositions,
				}
				scatters = append(scatters, scatter)
			}
		}
	}

	//fmt.Println("scatters:", scatters)
	return scatters
}

// positions 在grid中出现的坐标
// consecutiveCols连续出现了几列
func (m *m400122) checkWaysForIcon(grid [][]int, targetIcon int) (ways int, positions [][]int, consecutiveCols int) {
	ways = 1
	positions = make([][]int, 0)
	consecutiveCols = 0 //该图标连续出现几列

	for col := 0; col < m.Config.Column; col++ {
		matchPostions := make([][]int, 0) //保存图标索引
		for row := 0; row < m.Config.Row; row++ {
			if grid[row][col] == -9 || grid[row][col] == m.Config.ScatterIcon || grid[row][col] >= 12 { //>= 12表示硬币符号
				continue
			}
			if grid[row][col] == targetIcon {
				matchPostions = append(matchPostions, []int{row, col})
			}
		}
		if len(matchPostions) > 0 {
			ways *= len(matchPostions)
			consecutiveCols++
			positions = append(positions, matchPostions...)
		} else {
			break
		}
	}

	return
}

// 统计所有的硬币面额列表
func (m *m400122) getCoins(grid [][]int) (coins map[string]games.Coin) {
	coins = make(map[string]games.Coin)
	for i := 0; i < len(grid); i++ {
		for j := 0; j < len(grid[i]); j++ {
			icon := grid[i][j]
			if icon >= 12 && icon <= 19 {
				key := fmt.Sprintf("%d,%d", i, j)
				pos := []int{i, j}
				value, ok := m.Config.CoinMap[icon]
				if ok {
					coin := games.Coin{
						Position: pos,
						Icon:     icon,
						Value:    value,
					}
					coins[key] = coin
				}
			}
		}
	}
	return
}

// 判断一个石头的坐标 能否被蓝色闪电打碎
func (m *m400122) canBlueLightSmash(grid [][]int, pos []int) bool {
	rowidx := pos[0]
	colidx := pos[1]
	icon := grid[rowidx][colidx]
	if icon != -9 {
		return false
	}

	// 上方和下方 不能同时都为石头 才允许打碎
	if rowidx == 0 { //第一行 下方不是石头就允许打碎
		if grid[1][colidx] == -9 {
			return false
		} else {
			return true
		}
	}
	if rowidx == m.Config.Row-1 { //最后一行 上方不是石头就允许打碎
		if grid[rowidx-1][colidx] == -9 {
			return false
		} else {
			return true
		}
	}

	if grid[rowidx-1][colidx] == -9 && grid[rowidx+1][colidx] == -9 {
		return false
	} else {
		return true
	}
}

func (m *m400122) ZeroSpin(ctl int32, rd *rand.Rand) basic.ISpin {
	for {
		spin := m.Spin(rand.New(rd))
		pay := spin.Payout()
		if pay == 0 {
			return spin
		}
	}
}

func (m *m400122) Salting(spin basic.ISpin, salt *rand.Rand) basic.ISpin {
	s := spin.(*games.S400122)
	s.GameId = m.ID()
	s.Line = m.Line()
	s.Row = m.Config.Row
	s.Column = m.Config.Column
	// 最大赔付
	if s.Pays > int32(m.Config.MaxPayout) {
		s.Pays = int32(m.Config.MaxPayout)
	}
	return s
}

func (m m400122) Rule() string {
	gs := map[string]any{
		"mylinesInfo_v2": nil,
		"gamegroup":      "base",
		"doubleAssortment": []any{
			"off",
		},
		"maxBetPerGame_cents": nil,
		"betAssortment": []any{
			1, 2, 3, 5, 10, 25, 50, 125, 250,
		},
		"denomAssortment_cents": []any{
			1,
		},
		"minBetPerGame_cents": nil,
		"winValidation": map[string]any{
			"needcheck":                    false,
			"winlimit_fictiveRotate_gcurr": 25000000,
			"remaintime":                   86400000,
			"period":                       86400000,
			"isApproved":                   false,
			"isNotApproved":                false,
			"isWaitApprove":                false,
		},
		"buyBonus": map[string]any{
			"wasBuy":   0,
			"selectId": -1,
			"buyTotalBetK": []any{
				map[string]any{
					"id":      0,
					"cost":    100,
					"prefix2": "_BASE_FG",
					"rtp":     96.38,
				},
				map[string]any{
					"id":      1,
					"cost":    500,
					"prefix2": "_BASE_FG_DARK",
					"rtp":     96.4,
				},
				map[string]any{
					"id":      2,
					"cost":    180,
					"prefix2": "_BASE_FG_MIX",
					"rtp":     96.39,
				},
			},
		},
		"outRatesVolatility":    nil,
		"placedbet":             100,
		"gcurrency":             "",
		"gdenom":                1,
		"present":               "no",
		"betPerGame":            100,
		"betPerLine":            5,
		"nlines":                20,
		"phaseCur":              "finished",
		"phaseNext":             "toIdle",
		"maxBetPerGame_credits": 5000,
		"analInfo": map[string]any{
			"formula": map[string]any{
				"args": []any{
					"betPerLine",
					"nlines",
				},
				"body": "return(betPerLine * nlines/1)",
			},
			"formulaReverse": map[string]any{
				"args": []any{
					"betPerGame",
					"nlines",
				},
				"body": "return(betPerGame / nlines*1)",
			},
			"lineStyles": nil,
			"symbolNames": []any{
				"red", "green", "blue", "pink", "cross", "eye", "water", "vase", "scatter", "stone",
				"light1", "light2", "coin1", "coin2", "coin3", "coin5", "coin10", "coin20", "coin50", "coin100",
			},
			"baseReels": []any{
				[]any{1, 2, 3, 4, 5, 6, 7, 8, 1, 2, 3, 4, 5, 6, 7, 8, 4, 5, 6, 7, 8, 6, 7, 8},
				[]any{1, 2, 3, 4, 5, 6, 7, 8, 1, 2, 3, 4, 5, 6, 7, 8, 4, 5, 6, 7, 8, 6, 7, 8},
				[]any{1, 2, 3, 4, 5, 6, 7, 8, 1, 2, 3, 4, 5, 6, 7, 8, 4, 5, 6, 7, 8, 6, 7, 8},
				[]any{1, 2, 3, 4, 5, 6, 7, 8, 1, 2, 3, 4, 5, 6, 7, 8, 4, 5, 6, 7, 8, 6, 7, 8},
				[]any{1, 2, 3, 4, 5, 6, 7, 8, 1, 2, 3, 4, 5, 6, 7, 8, 4, 5, 6, 7, 8, 6, 7, 8},
				[]any{1, 2, 3, 4, 5, 6, 7, 8, 1, 2, 3, 4, 5, 6, 7, 8, 4, 5, 6, 7, 8, 6, 7, 8},
				[]any{1, 2, 3, 4, 5, 6, 7, 8, 1, 2, 3, 4, 5, 6, 7, 8, 4, 5, 6, 7, 8, 6, 7, 8},
			},
			"statTablo": map[string]any{
				"volatility": 8,
				"bigwin":     9,
				"epicwin":    9,
				"bonus":      2,
				"show":       1,
				"rtp":        96.36,
			},
			"maxWinFreq_small":     2522995,
			"VIP_maxWinFreq_small": 1254682,
			"arrlimits_winLimitK":  []any{5000, 25000},
			"scatterIds":           []any{8},
			"minScatters":          []any{3},
			"nSmallPicts":          8,
			"maxFlyersN":           15,
			"fullScreen_addFG":     3,
			"outRates_vipmode":     96.38,
			"mainMaxWinK":          5000,
			"volatility":           4,
			"sasAdditionalId":      "MMT",
			"sasPaytableId":        "MMT960",
		},
		"helpInfo": map[string]any{
			"paytable": []any{
				[]any{
					[]any{0, 4}, []any{7, 200}, []any{6, 100}, []any{5, 30}, []any{4, 20}, []any{3, 10},
				},
				[]any{
					[]any{1, 4}, []any{7, 150}, []any{6, 80}, []any{5, 24}, []any{4, 16}, []any{3, 8},
				},
				[]any{
					[]any{2, 4}, []any{7, 100}, []any{6, 60}, []any{5, 20}, []any{4, 12}, []any{3, 6},
				},
				[]any{
					[]any{3, 4}, []any{7, 80}, []any{6, 40}, []any{5, 16}, []any{4, 10}, []any{3, 5},
				},
				[]any{
					[]any{4, 4}, []any{7, 60}, []any{6, 20}, []any{5, 10}, []any{4, 8}, []any{3, 4},
				},
				[]any{
					[]any{5, 4}, []any{7, 40}, []any{6, 10}, []any{5, 8}, []any{4, 6}, []any{3, 3},
				},
				[]any{
					[]any{6, 4}, []any{7, 30}, []any{6, 8}, []any{5, 6}, []any{4, 4}, []any{3, 2},
				},
				[]any{
					[]any{7, 4}, []any{7, 20}, []any{6, 6}, []any{5, 4}, []any{4, 2}, []any{3, 1},
				},
				[]any{
					[]any{8, 8},
				},
				[]any{
					[]any{9, 32},
				},
				[]any{
					[]any{10, 8},
				},
				[]any{
					[]any{11, 8},
				},
				[]any{
					[]any{12, 8}, []any{1, 1},
				},
				[]any{
					[]any{13, 8}, []any{1, 2},
				},
				[]any{
					[]any{14, 8}, []any{1, 3},
				},
				[]any{
					[]any{15, 8}, []any{1, 5},
				},
				[]any{
					[]any{16, 8}, []any{1, 10},
				},
				[]any{
					[]any{17, 8}, []any{1, 20},
				},
				[]any{
					[]any{18, 8}, []any{1, 50},
				},
				[]any{
					[]any{19, 8}, []any{1, 100},
				},
			},
			"fg": map[string]any{
				"firstAward":   10,
				"nestingAward": 0,
				"limit":        18,
				"portions":     []any{0, 0, 0, 10, 11, 12, 15, 0},
			},
			"doubles": []any{
				[]any{"off", 0, 0},
			},
		},
		"doubleActive":           "off",
		"doubleActiveDbSettings": "off",
		"antiDynamiteBet":        nil,
		"dramshow":               nil,
		"versions": map[string]any{
			"server_core": "1.1",
			"server_game": "1.0",
			"server_math": "1.0",
		},
		"winlimits": map[string]any{
			"maxWinLimitK":         25000,
			"maxWin_gcurr":         nil,
			"needControlJackpot":   false,
			"winLimitK_gameconfig": 25000,
		},
		"isMaxFlag":       0,
		"isMaxFlag_lines": 0,
		"linesAssortment": []any{20},
		"linesPerCredit":  1,
		"reelstate":       0,
		"aux":             0,
		"startBox": []any{
			[]any{3, 5, 4, 4, 0, 6, 4},
			[]any{4, 5, 5, 6, 0, 7, 4},
			[]any{4, 6, 6, 2, 6, 7, 8},
			[]any{0, 7, 7, 4, 7, 7, 4},
			[]any{7, 7, 7, 6, 3, 7, 4},
			[]any{7, 16, 7, 0, 6, 5, 6},
			[]any{7, 6, 7, 0, 7, 5, 6},
		},
		"stopBox": []any{
			[]any{9, 9, 9, 9, 9, 9, 9},
			[]any{9, 9, 9, 9, 9, 9, 9},
			[]any{9, 9, 9, 9, 9, 9, 9},
			[]any{9, 9, 9, 9, 9, 9, 9},
			[]any{9, 9, 9, 9, 9, 9, 9},
			[]any{9, 9, 9, 9, 9, 9, 9},
			[]any{9, 9, 9, 9, 9, 9, 9},
			[]any{6, 4, 7, 7, 2, 7, 3},
			[]any{7, 6, 1, 3, 2, 4, 3},
			// ... 省略部分 stopBox 数据，结构类似
		},
		"USED_iterInfo":           true,
		"helpseed":                true,
		"iBugs":                   []any{0},
		"iMults":                  []any{0},
		"iMummy":                  []any{0},
		"sMult":                   1,
		"fMult":                   1,
		"setVip_inFreeSpinAlways": -1,
		"vipMode": map[string]any{
			"on":             0,
			"vipBetK":        1.25,
			"wasBuyVip":      0,
			"vip_noSpecSeed": true,
		},
		"bbLimitsWinK": []any{5000, 25000, 25000},
	}
	b, _ := json.Marshal(gs)
	return string(b)
}

func (m400122) InputCoef(ctl int32) int32 {
	switch ctl {
	case 1: //标准免费
		return 10000
	case 2: //HOT
		return 50000
	case 3: //选择FREE或HOT
		return 18000
	case 4:
		return 125
	default:
		return 100
	}
}

func (m m400122) MinPayout(ctl int32) int32 {
	mode, ok := m.Config.MinLimit[ctl]
	if !ok {
		return 0
	}
	return mode.X * m.Line()
}
