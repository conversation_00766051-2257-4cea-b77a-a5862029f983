package modules

import (
	"igameCommon/basic"
	"igameCommon/utils"
	"igameHttp/games"
	"math/rand"
)

type c10000014 struct {
	Row         int // 网格行数
	Column      int // 网格列数
	WildIcon    int32
	SpecialProb int32
	MulProb     int32
	PayoutTable map[int32]int // 赔率表
	IconWeight  map[int32]int
	MulWeight   map[int32]int
}

var _ = Factory.reg(basic.NewGeneral[*m10000014])

type m10000014 struct {
	Config               c10000014
	RandByWeight         *utils.RandomWeightPicker[int32, int]
	RandByWeightWithout0 *utils.RandomWeightPicker[int32, int]
	RandByMulWeight      *utils.RandomWeightPicker[int32, int]
}

func (m *m10000014) Init(config []byte) {
	m.Config = utils.ParseYAML[c10000014](config)
	m.RandByWeight = utils.NewRandomWeightPicker(m.Config.IconWeight)
	iconWeightWithout0 := make(map[int32]int)
	for k, v := range m.Config.IconWeight {
		if k != 0 {
			iconWeightWithout0[k] = v
		}
	}
	m.RandByWeightWithout0 = utils.NewRandomWeightPicker(iconWeightWithout0)
	m.RandByMulWeight = utils.NewRandomWeightPicker(m.Config.MulWeight)
}

func (m10000014) ID() int32 {
	return 10000014
}

func (m m10000014) Line() int32 { // 线数
	return 1
}

func (m m10000014) ClientMode() int32 {
	return basic.EnumClientMode.ONE
}

func (m m10000014) Exception(code int32) string {
	return games.S10000014{}.Exception(code)
}

func (m *m10000014) genGrid(rd *rand.Rand) ([]int32, bool) {
	grid := make([]int32, m.Config.Row*m.Config.Column)
	isSpec := false
	for row := 0; row < m.Config.Row; row++ {
		for col := 0; col < m.Config.Column; col++ {
			index := row*m.Config.Row + col
			if row == 1 {
				// 概率获取两个相同的图标
				if rd.Int31n(100) < m.Config.SpecialProb {
					grid[index] = m.RandByWeightWithout0.One(rd)
					grid[index+1] = m.RandByWeightWithout0.One(rd)
					grid[index+2] = grid[index]
					isSpec = true
				} else {
					grid[index] = m.RandByWeightWithout0.One(rd)
					grid[index+1] = m.RandByWeightWithout0.One(rd)
					grid[index+2] = m.RandByWeightWithout0.One(rd)
					isSpec = false
				}
			}
			if row == 2 {
				grid[index] = m.RandByWeight.One(rd)
			} else {
				grid[index] = m.RandByWeightWithout0.One(rd)
			}
		}
	}
	return grid, isSpec
}

func (m *m10000014) Spin(rd *rand.Rand) basic.ISpin {
	spin := games.S10000014{}
	spin.Grid, spin.IsSpec = m.genGrid(rd)

	pays, lines := m.parsePayout(spin.Grid, rd)
	spin.Pays = int32(pays)
	spin.Lines = lines

	return &spin
}

func (m *m10000014) parsePayout(grid []int32, rd *rand.Rand) (int, games.L10000014) {
	pays := 0
	line := games.L10000014{}

	// 无图标不中奖
	for _, icon := range grid {
		if icon == 0 {
			return 0, line
		}
	}

	for row := 0; row < m.Config.Row; row++ {
		for col := 0; col < m.Config.Column; col++ {
			index := row*m.Config.Row + col
			if row == 1 {
				// 只判断第二行
				icon1 := grid[index]
				icon2 := grid[index+1]
				icon3 := grid[index+2]

				// 判断三个图标是否一致
				if icon1 == icon2 && (icon2 == icon3 || icon2 == m.Config.WildIcon) && icon1 == icon3 {
					if icon2 == m.Config.WildIcon {
						if rd.Int31n(100) < m.Config.MulProb {
							mul := m.RandByMulWeight.One(rd)
							line.Mul = int(mul)
						}
					}
					if (icon1 >= 1 && icon1 <= 7) && (icon2 == m.Config.WildIcon || (icon2 >= 1 && icon2 <= 7)) && (icon3 >= 1 && icon3 <= 7) {
						if line.Mul > 0 {
							pays = m.Config.PayoutTable[icon1] * line.Mul
						} else {
							pays = m.Config.PayoutTable[icon1]
						}
						line.Icon = int16(icon1)
						line.Value = int32(m.Config.PayoutTable[icon1])
						line.Positions = []int{index, index + 1, index + 2}
						return pays, line
					}
				} else if (icon1 >= 1 && icon1 <= 3) && ((icon2 >= 1 && icon2 <= 3) || icon2 == m.Config.WildIcon) && (icon3 >= 1 && icon3 <= 3) {
					// 三个图标都在 1,2,3 范围内，且中间图标可以等于 wildIcon
					pays = m.Config.PayoutTable[0]
					line.Icon = int16(icon1)
					line.Value = int32(m.Config.PayoutTable[0])
					line.Positions = []int{index, index + 1, index + 2}
					return pays, line
				} else {
					// 其它情况不中奖
					return 0, line
				}
			}
		}
	}

	return 0, line
}

func (m *m10000014) ZeroSpin(ctl int32, rd *rand.Rand) basic.ISpin {
	for {
		spin := m.Spin(rand.New(rd))
		pay := spin.Payout()
		if pay == 0 {
			return spin
		}
	}
}

func (m *m10000014) Salting(spin basic.ISpin, salt *rand.Rand) basic.ISpin {
	r := spin.(*games.S10000014)
	r.GameId = m.ID()
	r.Line = m.Line()
	r.Row = m.Config.Row
	r.Column = m.Config.Column
	return r
}

func (m10000014) Rule() string {
	return ""
}

func (m10000014) InputCoef(ctl int32) int32 {
	return 100
}

func (m m10000014) MinPayout(ctl int32) int32 {
	return 0
}
