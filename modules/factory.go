package modules

import (
	"igameCommon/basic"
	"reflect"
)

type mFactory map[int32]func() basic.IModule

var Factory = mFactory{}

func (f mFactory) reg(h func() basic.IModule) error {
	f[h().ID()] = h
	return nil
}

func (f mFactory) Create(mid int32) basic.IModule {
	m := f[mid]()
	return m
}

func defaultCreate[T basic.IModule]() basic.IModule {
	mType := reflect.TypeFor[T]()
	switch mType.Kind() {
	// 为了兼容T是一个指针类型或者值类型
	// 返回始终为一个一级指针类型
	case reflect.Pointer:
		return reflect.New(mType.Elem()).Interface().(basic.IModule)
	default:
		return any(new(T)).(basic.IModule)
	}
}
