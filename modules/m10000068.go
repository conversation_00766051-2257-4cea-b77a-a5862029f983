package modules

import (
	"encoding/json"
	"igameCommon/basic"
	"igameCommon/utils"
	"igameHttp/games"
	"math/rand"
)

var _ = Factory.reg(basic.NewGeneral[*m10000068])

// m10000068 提供 Ways 玩法的基本实现
type m10000068 struct {
	Config       c10000068
	RandByWeight *utils.RandomWeightPicker[int16, int]
}

// 定义老虎机的基本配置
type c10000068 struct {
	Row         int   // 行数
	Column      int   // 列数
	WildIcon    int16 // wildId
	PayoutTable map[int16]int16
	MaxPayout   int32         // 最大派彩
	IconWeight  map[int16]int // 图标权重
	Pattern     [][]basic.Position
	SpecialProb int32
	MaxRetries  int
}

// ID 返回游戏ID
func (m *m10000068) ID() int32 {
	return 10000068 // 可根据不同游戏修改
}

func (m *m10000068) Line() int32 {
	return 5
}

func (m m10000068) ClientMode() int32 {
	return basic.EnumClientMode.ONE
}

func (m m10000068) Exception(code int32) string {
	return games.S10000068{}.Exception(code)
}

// Init 解析 YAML 配置
func (m *m10000068) Init(config []byte) {
	m.Config = utils.ParseYAML[c10000068](config)
	m.RandByWeight = utils.NewRandomWeightPicker(m.Config.IconWeight)
}

func (m m10000068) Rule() string {
	b, _ := json.Marshal(m.Config)
	return string(b)
}

func (m m10000068) InputCoef(mod int32) int32 {
	return 100
}

func (m m10000068) Salting(spin basic.ISpin, salt *rand.Rand) basic.ISpin {
	r := spin.(*games.S10000068)
	r.GameId = m.ID()
	r.Line = m.Line()
	r.Row = m.Config.Row
	r.Column = m.Config.Column
	r.Pattern = m.Config.Pattern
	return r
}

// Spin Fortune Mouse Feature + 重转
func (m m10000068) Spin(rd *rand.Rand) basic.ISpin {
	var spin games.S10000068
	if rd.Int31n(100) < m.Config.SpecialProb {
		spin = m.genSpec(rd)
	} else {
		spin = m.genNorm(rd)
	}

	if spin.Pays > m.Config.MaxPayout {
		spin.Pays = -1
	}
	return &spin
}

func (m m10000068) genNorm(rd *rand.Rand) games.S10000068 {
	grid := m.RandByWeight.More(m.Config.Row*m.Config.Column, rd)
	spin := games.S10000068{
		Pages: [][]int16{grid},
	}
	spin.Pays, spin.Lines, spin.Tenfold = m.parsePayout(grid)
	return spin
}

func (m m10000068) genSpec(rd *rand.Rand) games.S10000068 {
	var pages [][]int16 // 存储所有旋转的格子
	var pay int32       // 赢分
	var lines [][]int16 // 赢的线

	// 1. 生成初始格子
	var grid []int16 = make([]int16, m.Config.Row*m.Config.Column)
	for r := 0; r < m.Config.Row; r++ {
		// 中间一列设置为 Wild 符号
		idx := r*m.Config.Column + m.Config.Column/2
		grid[idx] = m.Config.WildIcon
		// 第一列和第三列随机生成
		grid[r*m.Config.Column] = m.RandByWeight.One(rd)                   // 第一列
		grid[r*m.Config.Column+m.Config.Column-1] = m.RandByWeight.One(rd) // 第三列
	}
	pages = append(pages, grid) // 记录初始格子

	// 2. 检查初始格子是否有赢
	pay, lines, _ = m.parsePayout(grid)
	if pay > 0 {
		// 如果初始格子就有赢，直接返回
		spin := games.S10000068{
			Pays:  pay,
			Pages: pages,
			Lines: lines,
		}
		return spin
	}

	// 3. 如果没有赢，重转第一和第三列
	for i := 0; i < m.Config.MaxRetries; i++ {
		// 复制上一个格子，保持中间列不变
		newGrid := make([]int16, len(grid))
		copy(newGrid, grid)
		// 只重转第一列和第三列
		for r := 0; r < m.Config.Row; r++ {
			newGrid[r*m.Config.Column] = m.RandByWeight.One(rd)                   // 第一列
			newGrid[r*m.Config.Column+m.Config.Column-1] = m.RandByWeight.One(rd) // 第三列
		}
		pages = append(pages, newGrid) // 记录每次重转的格子
		// 检查是否有赢
		pay, lines, _ = m.parsePayout(newGrid)
		if pay > 0 {
			break // 有赢时停止
		}
	}

	// 4. 构造结果
	spin := games.S10000068{
		Pays:  pay,
		Pages: pages,
		Lines: lines,
	}
	// 5. 设置 tenfold 标记（基于最后一个格子）
	spin.Pays, spin.Lines, spin.Tenfold = m.parsePayout(pages[len(pages)-1])
	return spin
}

func (m m10000068) parsePayout(grid []int16) (payout int32, lines [][]int16, tenfold bool) {
	var flag uint64
	for idx, pos := range m.Config.Pattern {
		x := pos[0].Index(int32(m.Config.Column))
		y := pos[1].Index(int32(m.Config.Column))
		z := pos[2].Index(int32(m.Config.Column))
		ico, pay := m.checkLine(grid[x], grid[y], grid[z])
		if pay > 0 {
			lines = append(lines, []int16{
				ico, int16(idx), pay,
				int16(x), int16(y), int16(z),
			})
			payout += int32(pay)
			flag |= 1<<x | 1<<y | 1<<z
		}
	}
	// 如果所有格子都至少参与过一次命中，则 tenfold
	if flag == (1<<uint(m.Config.Row*m.Config.Column))-1 {
		tenfold = true
	}
	return
}

// 判断一条线是否命中：万能符号（WildIcon）可替任何符号
func (m m10000068) checkLine(a, b, c int16) (int16, int16) {
	cnt := map[int16]int{a: 1, b: 1, c: 1}
	delete(cnt, m.Config.WildIcon)
	// 全是万能
	if len(cnt) == 0 {
		return m.Config.WildIcon, m.Config.PayoutTable[m.Config.WildIcon]
	}
	// 只有一种非万能符号，万能补齐即可
	if len(cnt) == 1 {
		for k := range cnt {
			return k, m.Config.PayoutTable[k]
		}
	}
	// 其它情况不成线
	return -1, 0
}

// ZeroSpin 生成一个不中奖的旋转
func (m *m10000068) ZeroSpin(ctl int32, rd *rand.Rand) basic.ISpin {
	for {
		spin := m.Spin(rand.New(rd))
		pay := spin.Payout()
		if pay == 0 {
			return spin
		}
	}
}

func (m m10000068) MinPayout(ctl int32) int32 {
	return 0
}
