package modules

import (
	"encoding/json"
	"igameCommon/basic"
	"igameCommon/utils"
	"igameHttp/games"
	"math/rand"
	"sort"
)

var _ = Factory.reg(basic.NewGeneral[*g10000005])

type g10000005 struct {
	Config             c10000005
	RandByWeight       *utils.RandomWeightPicker[int32, int32]
	RandByWeightNoWild *utils.RandomWeightPicker[int32, int32]
}

type c10000005 struct {
	Row          int32                 // 行数
	Column       int32                 // 列数
	WildIcon     int32                 // wildId
	PayoutTable  [][3]int32            // 赔率表 [符号ID, 连线数, 赔率*100]
	FreeSpin     FreeSpinConfig1000005 // 免费旋转配置
	BaseCoef     []int32               // 普通模式级联乘数
	FreeCoef     []int32               // 免费旋转模式级联乘数
	MaxPayout    int32                 // 最大派彩
	IconWeight   map[int32]int32       // 符号出现的权重
	PayoutWeight map[int32]int32       // 倍率出现的权重
	MinLimit     map[int32]struct {
		X     int32
		Limit map[string]int32
	}
}
type FreeSpinConfig1000005 struct {
	Icon       int32 // 触发免费旋转的符号ID
	Number     int   // 触发免费旋转的最小符号数
	FirstCount int   // 首次免费旋转次数
	MoreCount  int   // 额外触发免费旋转的次数
}

func (g *g10000005) Init(config []byte) {
	g.Config = utils.ParseYAML[c10000005](config)
	g.RandByWeight = utils.NewRandomWeightPicker(g.Config.IconWeight)
	noWildWeight := make(map[int32]int32)
	for icon, weight := range g.Config.IconWeight {
		if icon != g.Config.WildIcon {
			noWildWeight[icon] = weight
		}
	}
	g.RandByWeightNoWild = utils.NewRandomWeightPicker(noWildWeight)
}

func (g10000005) ID() int32 {
	return 10000005
}

func (m g10000005) Line() int32 { // 线数
	return 20
}

func (m g10000005) ClientMode() int32 {
	return basic.EnumClientMode.ONE
}

func (m g10000005) Exception(code int32) string {
	return (&games.S10000005{}).Exception(code)
}

func (g *g10000005) Spin(rd *rand.Rand) basic.ISpin {
	page := g.generatePage(rd, false)
	//partMul := int32(0)
	if page.PartMul > 0 {
		page.Pay = page.PartMul * page.Pages[len(page.Pages)-1].BasicPays
		page.TotalMul += page.PartMul
	} else {
		page.Pay = page.Pages[len(page.Pages)-1].BasicPays
		page.TotalMul += page.PartMul
	}
	totalPay := page.Pay
	if totalPay > g.Config.MaxPayout {
		return &games.S10000005{Pays: -1}
	}
	spin := &games.S10000005{
		SN:     0,
		Pays:   totalPay,
		Rounds: []games.WaysPage10000005{page},
	}
	freeSpins := 0
	if count, ok := page.Pages[len(page.Pages)-1].Special[g.Config.FreeSpin.Icon]; ok && count >= g.Config.FreeSpin.Number {
		freeSpins = g.Config.FreeSpin.FirstCount
		freeSpins += g.Config.FreeSpin.MoreCount * (count - g.Config.FreeSpin.Number)
	}
	// 处理免费旋转
	for i := 0; i < freeSpins; i++ {
		page = g.generatePage(rd, true)
		if count, ok := page.Pages[len(page.Pages)-1].Special[g.Config.FreeSpin.Icon]; ok && count >= g.Config.FreeSpin.Number {
			freeSpins += g.Config.FreeSpin.FirstCount
			freeSpins += g.Config.FreeSpin.MoreCount * (count - g.Config.FreeSpin.Number)
		}
		if len(page.Pages) > 1 {
			page.TotalMul = page.PartMul + spin.Rounds[i].TotalMul
			if page.TotalMul > 0 {
				page.Pay = page.TotalMul * page.Pages[len(page.Pages)-1].BasicPays
			} else {
				page.Pay = page.Pages[len(page.Pages)-1].BasicPays
			}
		} else if i == 0 {
			page.TotalMul = 0
		} else {
			page.TotalMul = spin.Rounds[i].TotalMul
		}

		totalPay = page.Pay
		if totalPay > g.Config.MaxPayout {
			return &games.S10000005{Pays: -1}
		}
		spin.Rounds = append(spin.Rounds, page)
		spin.Pays += totalPay
	}

	for _, p := range spin.Rounds {
		spin.SN += int32(len(p.Pages))
	}
	return spin
}

func (g *g10000005) ZeroSpin(ctl int32, rd *rand.Rand) basic.ISpin {
	for {
		spin := g.Spin(rand.New(rd))
		pay := spin.Payout()
		if pay == 0 {
			return spin
		}
	}
}

func (g *g10000005) Salting(spin0 basic.ISpin, salt *rand.Rand) basic.ISpin {
	r := spin0.(*games.S10000005)
	r.GameId = g.ID()
	r.Line = g.Line()
	r.Column = int(g.Config.Column)
	r.Row = int(g.Config.Row)
	return r
}

func (g g10000005) Rule() string {
	b, _ := json.Marshal(g.Config)
	return string(b)
}

func (g10000005) InputCoef(ctl int32) int32 {
	switch ctl {
	default:
		return 100
	case 1:
		return 10000
	}
}

func (g g10000005) generatePage(rd *rand.Rand, isFree bool) games.WaysPage10000005 {
	cols, rows := int(g.Config.Column), int(g.Config.Row)
	//var icons []int32
	Muls := utils.RandomFromWeighted(g.Config.PayoutWeight, cols, rd)
	grid := make([][]int32, cols)
	for col := 0; col < cols; col++ {
		grid[col] = make([]int32, rows)
		for row := 0; row < rows; row++ {
			grid[col][row] = g.RandByWeight.One(rd)
			// 第一列不生成 wildicon
			if grid[0][row] == g.Config.WildIcon {
				grid[0][row] = g.RandByWeightNoWild.One(rd)
			}
			//icons = append(icons, grid[col][row])
		}
	}
	//if isFree {
	//	for col := 0; col < cols; col++ {
	//		for row := 0; row < rows; row++ {
	//			//免费游戏中不要套免费了
	//			if grid[col][row] == g.Config.FreeSpin.Icon {
	//				grid[col][row] = utils.RandomFromWeighted(g.Config.IconWeight, 1, rd)[0]
	//			}
	//		}
	//	}
	//}
	pages := games.WaysPage10000005{}
	newDrop := make([][]int32, cols)
	basicPays := int32(0)
	dogIcon := make([]int32, 5)
	for {
		wins := g.calcWays(grid)
		special := g.countScatter(grid)
		for _, win := range wins {
			basicPays += win.WinAmount
		}

		icons := make([]int32, 0, cols*rows)
		for col := 0; col < cols; col++ {
			for row := 0; row < rows; row++ {
				icons = append(icons, grid[col][row])
			}
		}

		newDrop_ := make([][]int32, cols)
		for col := 0; col < cols; col++ {
			newDrop_[col] = []int32{}
			for row := 0; row < len(newDrop[col]); row++ {
				newDrop_[col] = append(newDrop_[col], newDrop[col][row])
			}
		}
		dogIcon = g.countDicTag(grid, dogIcon)
		page := games.Page10000005{BasicPays: basicPays, Special: special, DropIco: icons, Wins: wins, NewDrop: newDrop_, Muls: Muls, DicTag: dogIcon}
		pages.Pages = append(pages.Pages, page)
		if len(wins) == 0 {
			partMul := g.getMul(dogIcon, Muls)
			pages.PartMul = partMul
			break
		}

		mobiless := g.applyWins(grid, wins)
		grid, newDrop = g.dropSymbols(grid, rd)
		for col := 0; col < cols; col++ {
			for row := 0; row < rows-1; row++ {
				item := mobiless[col].Mobiles[row]
				mobiless[col].Mobiles[row].Card = int(grid[item.Tcol-1][item.Trow])
			}
		}
		pages.Pages[len(pages.Pages)-1].Mobiless = mobiless

	}
	return pages
}

// 统计免费旋转图标数量
func (g g10000005) countScatter(grid [][]int32) map[int32]int {
	special := make(map[int32]int)
	for col := range grid {
		for row := 1; row < len(grid[col]); row++ {
			if grid[col][row] == g.Config.FreeSpin.Icon {
				special[g.Config.FreeSpin.Icon]++
			}
		}
	}
	return special
}

// 获取触发赔率图标值
func (g g10000005) countDicTag(grid [][]int32, dogIcon []int32) []int32 {
	for col := range grid {
		for row := 1; row < len(grid[col]); row++ {
			if grid[col][row] == 11 {
				dogIcon[col]++
			}
		}
	}
	return dogIcon
}

// 消除计算
func (g g10000005) calcWays(grid [][]int32) []games.Win10000005 {
	var wins []games.Win10000005
	for symbol := int32(1); symbol < 13; symbol++ {
		ways := 1
		var positions []games.Position10000005
		line := 0
		for col := 0; col < len(grid); col++ {
			count := 0
			for row := 1; row < len(grid[col]); row++ {
				if grid[col][row] == symbol || grid[col][row] == g.Config.WildIcon {
					count++
					positions = append(positions, games.Position10000005{Col: col, Row: row})
				}
			}
			if count > 0 {
				ways *= count
				line++
			} else {
				break
			}
		}
		if line >= 3 {
			winAmount := g.getPayout(symbol, line)
			winAmount = winAmount * int32(ways)
			wins = append(wins, games.Win10000005{Symbol: symbol, Ways: ways, WinAmount: winAmount, Positions: positions})
		}
	}
	return wins
}

func (g *g10000005) getPayout(symbol int32, count int) int32 {
	for _, payout := range g.Config.PayoutTable {
		if payout[0] == symbol && int(payout[1]) == count {
			return payout[2]
		}
	}
	return 0
}

func (g *g10000005) applyWins(grid [][]int32, wins []games.Win10000005) []games.Mobiless10000005 {
	cols := len(grid)
	rows := len(grid[0])
	toRemove := make([][]bool, cols)
	for col := range grid {
		toRemove[col] = make([]bool, rows)
	}
	for _, win := range wins {
		for _, pos := range win.Positions {
			col, row := pos.Col, pos.Row
			if toRemove[col][row] {
				continue
			}
			toRemove[col][row] = true
		}
	}
	mobiless := make([]games.Mobiless10000005, cols)
	for col := 0; col < cols; col++ {
		mobiles := []games.Mobiles10000005{}
		for row := 0; row < rows; row++ {
			b := false
			if toRemove[col][row] {
				grid[col][row] = -1
				for i := 0; i < len(mobiles); i++ {
					mobiles[i].Trow = mobiles[i].Trow + 1
				}
				b = true
			}
			if row > 0 && row < len(grid[col]) {
				item := games.Mobiles10000005{
					Card: int(grid[col][row]),
					Row:  row,     //掉落之前的位置
					Col:  col + 1, //掉落之前的位置
					Type: 1,
					Trow: row,     //掉落之后的位置
					Tcol: col + 1, //掉落之后的位置
				}
				if b {
					item.Trow = 1
					item.Row = -1
					item.Col = -1
				}
				mobiles = append(mobiles, item)

			}
			sort.Slice(mobiles, func(i, j int) bool { return mobiles[i].Trow > mobiles[j].Trow })
			mobiless[col] = games.Mobiless10000005{
				Mobiles: mobiles,
				Col:     col + 1,
			}

		}
	}
	return mobiless
}

//func (g g10000005) getCascadingMultiplier(grid [][]int32, Muls []int32) int32 {
//	var count int32
//	for i, muls := range g.countDicTag(grid) {
//		if muls != 0 {
//			count += Muls[i]
//		}
//	}
//	return count
//}

func (g *g10000005) dropSymbols(grid [][]int32, rd *rand.Rand) ([][]int32, [][]int32) {
	cols := len(grid)
	rows := len(grid[0])
	newDrop := make([][]int32, cols)
	for col := 0; col < cols; col++ {
		newDrop[col] = []int32{}
		newCol := make([]int32, rows)
		dest := rows - 1
		for row := rows - 1; row >= 0; row-- {
			if grid[col][row] != -1 {
				newCol[dest] = grid[col][row]
				dest--
			}
		}
		if dest >= 0 && col != 0 {
			newCount := dest + 1
			//newIcos := utils.RandomFromWeighted(g.Config.IconWeight, newCount, rd)
			for fill := 0; fill < newCount; fill++ {
				newCol[fill] = g.RandByWeight.One(rd)
				newDrop[col] = append(newDrop[col], newCol[fill])
			}
		}
		if dest >= 0 && col == 0 {
			newCount := dest + 1
			for fill := 0; fill < newCount; fill++ {
				newCol[fill] = g.RandByWeightNoWild.One(rd)
				newDrop[col] = append(newDrop[col], newCol[fill])
			}
		}
		grid[col] = newCol
	}
	return grid, newDrop
}

func (g g10000005) getMul(dicTag, Muls []int32) int32 {
	var count int32
	for i, mul := range dicTag {
		if mul > 0 {
			count += Muls[i]
		}
	}
	return count
}

// 不同的算法模式的最小赔率 单线
func (m g10000005) MinPayout(ctl int32) int32 {
	mode, ok := m.Config.MinLimit[ctl]
	if !ok {
		return 0
	}
	return mode.X * m.Line()
}
