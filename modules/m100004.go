package modules

import (
	"encoding/json"
	"igameCommon/basic"
	"igameCommon/utils"
	"igameHttp/games"
	"math/rand"
	"slices"
)

var _ = Factory.reg(basic.NewGeneral[*m100004])

type m100004 struct {
	Config c100004
}

func (m m100004) ID() int32 {
	return 100004
}

func (m m100004) Line() int32 {
	return 20
}

func (m m100004) ClientMode() int32 {
	return basic.EnumClientMode.ONE
}

func (m m100004) Exception(code int32) string {
	return games.S100004{}.Exception(code)
}

func (g *m100004) Init(config []byte) {
	g.Config = utils.ParseYAML[c100004](config)
}

func (m m100004) ZeroSpin(ctl int32, salt *rand.Rand) basic.ISpin {
	return games.S100004{
		Pages: []games.P100004{{
			Grid: make(basic.Grid, m.Config.Column*m.Config.Row),
		}},
	}
}

func (m m100004) Spin(rd *rand.Rand) basic.ISpin {
	var grid []basic.Ico
	var page games.P100004
	// icons := utils.MapKeysOrdered(m.Config.PayoutTable)
	utils.Loop(func() bool {
		// grid = utils.RandomFrom(icons, m.Config.Column*m.Config.Row, rd)
		grid = utils.RandomFromWeighted(m.Config.IconWeight, m.Config.Column*m.Config.Row, rd)
		page = m.parseToPage(grid, 0)
		return page.Pay != -1
	}, 10)
	spin := games.S100004{
		Pays:      page.Pay,
		FreeSpins: page.Free,
		Pages:     []games.P100004{page},
	}
	if page.Pay <= 0 || page.Free == 0 {
		return spin
	}
	for range page.Free {
		utils.Loop(func() bool {
			grid = utils.RandomFromWeighted(m.Config.FreeSpin.FreeModeIcons, m.Config.Column*m.Config.Row, rd)
			page = m.parseToPage(grid, len(spin.Pages))
			return page.Pay != -1
		}, 10)
		if page.Pay == -1 {
			return games.S100004{Pays: -1}
		}
		spin.Pages = append(spin.Pages, page)
		spin.Pays += page.Pay
	}
	return spin
}

func (m *m100004) parseToPage(grid []basic.Ico, index int) games.P100004 {
	var payout int32
	var freeSpin int16
	var patterns uint64
	for i := 0; i < len(grid); i += int(m.Config.Column) {
		if m.isCommon(grid[i]) || (index > 0 && grid[i] == m.Config.FreeSpin.Icon) {
			return games.P100004{Pay: -1}
		}
	}
	wildToCommon := make([]bool, m.Config.Column)
	if index > 0 {
		for i := 0; i < len(grid); i++ {
			if grid[i] == m.Config.FreeSpin.Icon {
				wildToCommon[i%int(m.Config.Column)] = true
			}
		}
	}
	freeIcons := 0
	for i, item := range grid {
		if !m.isFreeSpin(item) {
			continue
		}
		freeIcons++
		for j := i - int(m.Config.Column); j >= 0; j -= int(m.Config.Column) {
			if m.isFreeSpin(grid[j]) {
				return games.P100004{Pay: -1}
			}
		}
	}
	if freeIcons > len(m.Config.FreeSpin.Count) {
		return games.P100004{Pay: -1}
	}
	if freeIcons > 0 {
		freeSpin = m.Config.FreeSpin.Count[freeIcons-1]
		payout += m.Config.PayoutTable[m.Config.FreeSpin.Icon][freeIcons-1]
	}
	for i, pattern := range m.Config.Pattern {
		index := pattern[0].Index(m.Config.Column)
		firstIco := grid[index]
		if firstIco == basic.IcoEmpty || m.isFreeSpin(firstIco) {
			continue
		}
		count := 0
		for i, pos := range pattern {
			ico := grid[pos.Index(m.Config.Column)]
			isWild := wildToCommon[pos.Column()]
			if ico != firstIco && !m.isCommon(ico) && !wildToCommon[pos.Column()] && !isWild {
				break
			}
			count = i + 1
		}
		if add := m.Config.PayoutTable[firstIco][count-1]; add > 0 {
			payout += add
			patterns |= 1 << i
		}
	}
	return games.P100004{
		Grid:    grid,
		Pay:     payout,
		Free:    freeSpin,
		Pattern: patterns,
	}
}

func (m m100004) Salting(spin0 basic.ISpin, salt *rand.Rand) basic.ISpin {
	spin := spin0.(games.S100004)
	for index, page := range spin.Pages {
		m.padding(&page, index, salt)
	}
	b, _ := json.Marshal(m.parseDatail(&spin))
	spin.Detail = string(b)
	return spin
}

func (m m100004) padding(page *games.P100004, index int, rd *rand.Rand) {
	icons := utils.MapKeysOrdered(m.Config.PayoutTable)
	icons = utils.Filter(icons, func(i basic.Ico) bool {
		if i == m.Config.CommonIcon || i == m.Config.FreeSpin.Icon {
			return false
		}
		return true
	})
	if index > 0 {
		icons = utils.Filter(icons, func(i basic.Ico) bool {
			return i != m.Config.FreeSpin.Icon && m.Config.FreeSpin.FreeModeIcons[i] > 0
		})
	}
	grid := slices.Clone(page.Grid)
	visit := make([]bool, len(grid))
	for _, pattern := range m.Config.Pattern {
		index := pattern[0].Index(m.Config.Column)
		firstIco := grid[index]
		if firstIco == basic.IcoEmpty {
			continue
		}
		for _, pos := range pattern {
			idx := pos.Index(m.Config.Column)
			ico := grid[idx]
			if ico != firstIco && !m.isFreeSpin(firstIco) && !m.isCommon(ico) {
				break
			}
			visit[idx] = true
		}
	}
	for i := range grid {
		if m.isCommon(grid[i]) || m.isFreeSpin(grid[i]) || visit[i] {
			continue
		}
		grid[i] = basic.IcoEmpty
	}
	for count := 0; count < 1000; count++ { // 1000后几乎不需要保底算法
		g := slices.Clone(grid)
		for i := 0; i < len(grid); i++ {
			if g[i] == basic.IcoEmpty {
				g[i] = icons[rd.Intn(len(icons))]
			}
		}
		if page.Pay == m.parseToPage(g, index).Pay {
			copy(page.Grid, g)
			return
		}
	}
}

func (m m100004) Rule() string {
	b, _ := json.Marshal(m.Config)
	return string(b)
}

func (m *m100004) isCommon(ico basic.Ico) bool {
	return m.Config.CommonIcon == ico
}

func (m *m100004) isFreeSpin(ico basic.Ico) bool {
	return m.Config.FreeSpin.Icon == ico
}

func (m *m100004) parseDatail(spin *games.S100004) any {
	result := map[string]any{
		"spinResult": []any{m.parsePage(spin.Pages[0], false)},
	}
	if len(spin.Pages) == 1 {
		return result
	}
	result["hasScatter"] = true
	subs := make([]any, 0, len(spin.Pages)-1)
	for i := 1; i < len(spin.Pages); i++ {
		subs = append(subs, m.parsePage(spin.Pages[i], true))
	}
	result["scatterResult"] = subs
	return result
}

func (m *m100004) parsePage(page games.P100004, freeSpin bool) any {
	firstGrid := page.Grid.Turn(m.Config.Column)
	result := map[string]any{
		"icons":    firstGrid.IDs(),
		"winMulti": float64(page.Pay) * 100 / 20, // 百分比
	}
	if page.Free > 0 {
		var specialIndex []int
		for i, v := range firstGrid {
			if m.isFreeSpin(v) {
				specialIndex = append(specialIndex, i)
			}
		}
		result["specialIndex"] = specialIndex
	}
	lines := []map[string]any{}
	patterns := page.Pattern
	patternID := -1
	grid := page.Grid
	wildToCommon := make([]bool, m.Config.Column)
	if freeSpin {
		for i := 0; i < len(grid); i++ {
			if grid[i] == m.Config.FreeSpin.Icon {
				wildToCommon[i%int(m.Config.Column)] = true
			}
		}
	}
	flag := make([]int, m.Config.Column)
	for ; patterns > 0; patterns >>= 1 {
		patternID++
		if patterns&1 == 0 {
			continue
		}
		pattern := m.Config.Pattern[patternID]
		index := pattern[0].Index(m.Config.Column)
		firstIco := grid[index]
		if m.isFreeSpin(firstIco) {
			continue
		}
		icosIdx := []int32{}
		count := 0
		for _, pos := range pattern {
			ico := grid[pos.Index(m.Config.Column)]
			if ico != firstIco && !m.isCommon(ico) && !wildToCommon[pos.Column()] {
				break
			}
			count++
			icosIdx = append(icosIdx, pos.Row()+pos.Column()*3)
			flag[pos.Column()] |= 1 << pos.Row()
		}
		payout := m.Config.PayoutTable[firstIco][count-1]
		lines = append(lines, map[string]any{
			"serialNo": patternID,
			"iconsNo":  icosIdx,
			"payout":   payout * 100 / 20, // 百分比
		})
	}
	result["lines"] = lines
	if !freeSpin {
		return result
	}
	icons := result["icons"].([]int16)
	var specialIndex []int
	for i := 1; i < len(grid); i++ {
		if grid[i] == m.Config.FreeSpin.Icon {
			row := i / int(m.Config.Column)
			column := i % int(m.Config.Column)
			index := column*int(m.Config.Row) + row
			specialIndex = append(specialIndex, index)
		}
	}
	result["icons"] = icons
	result["specialIndex"] = specialIndex
	return result
}

func (m m100004) InputCoef(ctl int32) int32 {
	return 100
}

func (m m100004) MinPayout(int32) int32 {
	return 0
}

type c100004 struct {
	Row         int32
	Column      int32
	Pattern     [][]basic.Position
	PayoutTable map[basic.Ico][]int32
	IconWeight  map[basic.Ico]int32
	CommonIcon  basic.Ico
	FreeSpin    struct {
		Icon          basic.Ico
		Count         []int16
		FreeModeIcons map[basic.Ico]int32
	}
	MaxPayout int32
}
