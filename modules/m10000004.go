package modules

import (
	"encoding/json"
	"fmt"
	"igameCommon/basic"
	"igameCommon/utils"
	"igameHttp/games"
	types "igameHttp/types/pgslot"
	"math/rand"
	"strconv"
)

var _ = Factory.reg(basic.NewGeneral[*m10000004])

type c10000004 struct {
	GameID            int // 游戏ID
	RuleID            int // 生成器规则ID
	Row               int // 网格行数
	Column            int // 网格列数
	MaxPayout         int32
	WildIcon          int16
	ScatterIcon       int16
	MultipleIcon      int16
	PayoutTable       map[int16][]int16
	NormIconWeight    map[int16]int
	FreeIconWeight    map[int16]int
	LargeSymbolWeight map[int16]int
	SymbolFrameWeight map[int16]int
	MultipleWeight    map[int16]int          //奖金翻倍符号权重
	FreeSpin          FreeSpinConfig10000004 // Free spin config
	FreeSpinWeight    map[int16]int
}

type FreeSpinConfig10000004 struct {
	Icon       int16 // Scatter icon ID
	MinCount   int   // Minimum scatters for free spins
	Count      int   // Base free spin count
	ExtraCount int   // 每个额外的夺宝符号再加几次旋转
}

type m10000004 struct {
	Config                       c10000004
	RandByWeight                 *utils.RandomWeightPicker[int16, int]
	RandByWeightFree             *utils.RandomWeightPicker[int16, int]
	RandByWeightNoWild           *utils.RandomWeightPicker[int16, int]
	RandByWeightFreeNoWild       *utils.RandomWeightPicker[int16, int]
	RandByWeightNoWildAndScatter *utils.RandomWeightPicker[int16, int]
	RandByWeightFreeSpin         *utils.RandomWeightPicker[int16, int]
	RandByWeightLarge            *utils.RandomWeightPicker[int16, int]
	RandByWeightFrame            *utils.RandomWeightPicker[int16, int]
	RandByMultipleWeight         *utils.RandomWeightPicker[int16, int]
}

func (m *m10000004) Init(config []byte) {
	m.Config = utils.ParseYAML[c10000004](config)
	m.RandByWeight = utils.NewRandomWeightPicker(m.Config.NormIconWeight)
	m.RandByWeightFree = utils.NewRandomWeightPicker(m.Config.FreeIconWeight)
	noWildWeight := make(map[int16]int)
	noWildAndScatterWeight := make(map[int16]int)
	for icon, weight := range m.Config.NormIconWeight {
		if icon != m.Config.WildIcon {
			noWildWeight[icon] = weight
		}
		if icon != m.Config.WildIcon && icon != m.Config.ScatterIcon && icon != m.Config.MultipleIcon {
			noWildAndScatterWeight[icon] = weight
		}
	}
	m.RandByWeightNoWild = utils.NewRandomWeightPicker(noWildWeight)
	m.RandByWeightNoWildAndScatter = utils.NewRandomWeightPicker(noWildAndScatterWeight)

	noWildWeightFree := make(map[int16]int)
	for icon, weight := range m.Config.FreeIconWeight {
		if icon != m.Config.WildIcon {
			noWildWeightFree[icon] = weight
		}
	}
	m.RandByWeightFreeNoWild = utils.NewRandomWeightPicker(noWildWeightFree)

	m.RandByWeightFreeSpin = utils.NewRandomWeightPicker(m.Config.FreeSpinWeight)
	m.RandByWeightLarge = utils.NewRandomWeightPicker(m.Config.LargeSymbolWeight)
	m.RandByWeightFrame = utils.NewRandomWeightPicker(m.Config.SymbolFrameWeight)
	m.RandByMultipleWeight = utils.NewRandomWeightPicker(m.Config.MultipleWeight)
}

func (m10000004) ID() int32 {
	return 10000004
}

func (m m10000004) Line() int32 { // 线数
	return 20
}

func (m m10000004) ClientMode() int32 {
	return basic.EnumClientMode.ONE
}

func (m m10000004) Exception(code int32) string {
	b, _ := json.Marshal(map[string]any{
		"code":   0,
		"status": true,
		"msg":    "",
		"data":   "{\"cleans\":null,\"Scatters\":null,\"frameInfos\":null,\"openfree\":0,\"TotalFree\":0,\"Ways\":0,\"mul\":0,\"IncreaseMul\":0,\"pos\":0,\"_turntable\":[],\"curReward\":0,\"Lv\":10,\"gold\":0,\"gamble\":90,\"lineCount\":20,\"usergold\":0,\"winType\":0,\"hitline\":null,\"openmarry\":0,\"FreeWinGold\":0,\"isstartfree\":false,\"prizeType\":0,\"minBetScore\":0,\"plaindatas\":null,\"fn\":\"sc_gambleone_slotcb\",\"_msgid\":354,\"cc\":0,\"result\":-301,\"message\":\"Gold not enough\"}",
	})
	return string(b)
}

func (m *m10000004) genGrid(rd *rand.Rand, isFreeModel bool) []games.Cell10000004 {
	grid := make([]games.Cell10000004, m.Config.Row*m.Config.Column)
	for row := 0; row < m.Config.Row; row++ {
		for col := 0; col < m.Config.Column; col++ {
			index := row*m.Config.Column + col
			if index == 0 || index == m.Config.Column-1 {
				grid[index] = games.Cell10000004{Symbol: 0, Frame: 0}
			} else {
				if isFreeModel {
					if col == 0 || col == m.Config.Column-1 {
						grid[index] = games.Cell10000004{Symbol: m.RandByWeightFreeNoWild.One(rd), Frame: 0}
					} else {
						grid[index] = games.Cell10000004{Symbol: m.RandByWeightFree.One(rd), Frame: 0}
					}
				} else {
					if col == 0 {
						grid[index] = games.Cell10000004{Symbol: m.RandByWeightNoWild.One(rd), Frame: 0}
					} else {
						grid[index] = games.Cell10000004{Symbol: m.RandByWeight.One(rd), Frame: 0}
					}
				}
			}

			if grid[index].Symbol == m.Config.MultipleIcon { //翻倍图标
				grid[index].Mul = m.RandByMultipleWeight.One(rd)
			}
		}
	}

	// 随机选择 2-4 列，在卷轴 2-5 (索引 1-4) 生成 2-3 个垂直连续 Silver frame 格子
	numColsWithFrames := rd.Intn(3) + 2 // 随机 2-4 列
	availableCols := []int{1, 2, 3, 4}  // 卷轴 2-5
	rd.Shuffle(len(availableCols), func(i, j int) {
		availableCols[i], availableCols[j] = availableCols[j], availableCols[i]
	})

	for i := 0; i < numColsWithFrames; i++ {
		col := availableCols[i]
		// 随机选择连续长度 (2-4)
		length := int(m.RandByWeightLarge.One(rd))
		// 随机选择起始行 (确保在行范围内)
		maxStartRow := m.Config.Row - length
		if maxStartRow < 1 {
			maxStartRow = 1
		}
		startRow := rd.Intn(maxStartRow) + 1
		// 随机选择一个普通符号
		symbol := m.RandByWeightNoWildAndScatter.One(rd)
		frame := m.RandByWeightFrame.One(rd)
		// 填充垂直连续格子
		for r := startRow; r < startRow+length && r < m.Config.Row; r++ {
			index := r*m.Config.Column + col
			grid[index] = games.Cell10000004{Symbol: symbol, Frame: frame}
		}
	}

	return grid
}

func (m *m10000004) Spin(rd *rand.Rand) basic.ISpin {
	grid := m.genGrid(rd, false)
	spin := &games.S10000004{
		GameId:       int32(m.ID()),
		CascadeCount: 0,
		Line:         m.Line(),
		Row:          m.Config.Row,
		Column:       m.Config.Column,
	}
	cascadeIndex := 0
	_ = m.parsePayout(grid, spin, rd, 0)

	spin.Rounds = append(spin.Rounds, spin)

	if spin.IsStartFree && spin.IsFreeModel {
		for {
			grid = m.genGrid(rd, true)
			freespin := &games.S10000004{
				GameId:       int32(m.ID()),
				CascadeCount: 0,
				Line:         m.Line(),
				Row:          m.Config.Row,
				Column:       m.Config.Column,
				Multiplier:   spin.Multiplier,
				FreeSpins:    0,
				IsFreeModel:  true,
			}
			cascadeIndex = m.parsePayout(grid, freespin, rd, cascadeIndex)
			spin.Pays += freespin.Pays
			spin.FreeSpins += freespin.FreeSpins
			spin.FreeSpins--
			spin.Rounds = append(spin.Rounds, freespin)

			if spin.FreeSpins == 0 {
				break
			}
		}
	}

	return spin
}

func (m *m10000004) parsePayout(grid []games.Cell10000004, spin *games.S10000004, rd *rand.Rand, cascadeIndex int) int {
	currentGrid := make([]games.Cell10000004, len(grid))
	copy(currentGrid, grid)

	for {
		scatterCount := 0    //记录夺宝图标数
		var catMul int16 = 0 //记录翻倍数
		for _, cell := range currentGrid {
			if cell.Symbol == m.Config.FreeSpin.Icon {
				scatterCount++
			}
			if cell.Symbol == m.Config.MultipleIcon {
				catMul += cell.Mul
			}
		}

		//计算赢奖
		if catMul == 0 {
			catMul = 1
		}
		wins := m.calculateWins(currentGrid, catMul)

		var pagePayout int32
		for _, win := range wins {
			pagePayout += win.Value
		}
		columnGroupList, maxGroupId := m.getColumnGroupList(currentGrid)
		columnMulGroupList := m.getColumnMulGroupList(currentGrid, maxGroupId)

		page := games.P10000004{
			Grid:               currentGrid,
			Pay:                pagePayout,
			Lines:              wins,
			CascadeIndex:       cascadeIndex,
			ScatterCount:       scatterCount,
			CatMul:             catMul,
			ColumnGroupList:    columnGroupList,
			ColumnMulGroupList: columnMulGroupList,
		}

		if len(wins) > 0 {
			mobiles, newGrid := m.performCascade(currentGrid, wins, rd, spin)
			page.Mobiles = mobiles
			spin.Pages = append(spin.Pages, page)
			spin.Pay += pagePayout
			spin.Pays += pagePayout
			currentGrid = newGrid
			cascadeIndex++
		} else {
			spin.Pages = append(spin.Pages, page)

			if pagePayout > 0 {
				spin.Pays += pagePayout
			}
			break
		}
	}
	spin.CascadeCount = len(spin.Pages)
	// 免费判断
	spin.Scatters = m.checkScatters(currentGrid, spin, rd)

	return cascadeIndex
}

// 被合并的格子信息
func (m *m10000004) getColumnGroupList(grid []games.Cell10000004) (map[string][]games.ColumnGroupInfo, int) {
	columnGroupList := make(map[string][]games.ColumnGroupInfo)

	groupID := 1
	for col := 0; col < m.Config.Column; col++ {
		pointlist := make([]types.Point, 0)
		for row := 0; row < m.Config.Row; row++ {
			idx := row*m.Config.Column + col
			cell := grid[idx]
			if cell.Frame > 0 {
				if len(pointlist) == 0 {
					pointlist = append(pointlist, types.Point{
						X: row + 1,
						Y: col + 1,
					})
				} else {
					previdx := (pointlist[0].X-1)*m.Config.Column + (pointlist[0].Y - 1)
					if grid[previdx].Symbol == cell.Symbol {
						pointlist = append(pointlist, types.Point{
							X: row + 1,
							Y: col + 1,
						})
					} else {
						columnGroupList[strconv.Itoa(col+1)] = append(columnGroupList[strconv.Itoa(col+1)], games.ColumnGroupInfo{
							GroupID:   groupID,
							Cardid:    int(grid[previdx].Symbol),
							Mul:       int(grid[previdx].Mul),
							PointList: pointlist,
						})
						groupID++
						pointlist = make([]types.Point, 0)
						pointlist = append(pointlist, types.Point{
							X: row + 1,
							Y: col + 1,
						})
					}
				}
			} else {
				if len(pointlist) > 0 {
					previdx := (pointlist[0].X-1)*m.Config.Column + (pointlist[0].Y - 1)
					columnGroupList[strconv.Itoa(col+1)] = append(columnGroupList[strconv.Itoa(col+1)], games.ColumnGroupInfo{
						GroupID:   groupID,
						Cardid:    int(grid[previdx].Symbol),
						Mul:       int(grid[previdx].Mul),
						PointList: pointlist,
					})
					groupID++
					pointlist = make([]types.Point, 0)
				}
			}

		}
	}

	return columnGroupList, groupID

}

// 翻倍的的格子信息
func (m *m10000004) getColumnMulGroupList(grid []games.Cell10000004, startGroupID int) map[string][]games.ColumnGroupInfo {
	columnMulGroupList := make(map[string][]games.ColumnGroupInfo)
	groupID := startGroupID
	for col := 0; col < m.Config.Column; col++ {
		pointlist := make([]types.Point, 0)
		for row := 0; row < m.Config.Row; row++ {
			idx := row*m.Config.Column + col
			cell := grid[idx]
			if cell.Symbol == m.Config.MultipleIcon {
				if len(pointlist) == 0 {
					pointlist = append(pointlist, types.Point{
						X: row + 1,
						Y: col + 1,
					})
				} else {
					previdx := (pointlist[0].X-1)*m.Config.Column + (pointlist[0].Y - 1)
					if grid[previdx].Mul == cell.Mul {
						pointlist = append(pointlist, types.Point{
							X: row + 1,
							Y: col + 1,
						})
					} else {
						columnMulGroupList[strconv.Itoa(col+1)] = append(columnMulGroupList[strconv.Itoa(col+1)], games.ColumnGroupInfo{
							GroupID:   groupID,
							Cardid:    int(grid[previdx].Symbol),
							Mul:       int(grid[previdx].Mul),
							PointList: pointlist,
						})
						groupID++
						pointlist = make([]types.Point, 0)
					}
				}
			} else {
				if len(pointlist) > 0 {
					previdx := (pointlist[0].X-1)*m.Config.Column + (pointlist[0].Y - 1)
					columnMulGroupList[strconv.Itoa(col+1)] = append(columnMulGroupList[strconv.Itoa(col+1)], games.ColumnGroupInfo{
						GroupID:   groupID,
						Cardid:    int(grid[previdx].Symbol),
						Mul:       int(grid[previdx].Mul),
						PointList: pointlist,
					})
					groupID++
					pointlist = make([]types.Point, 0)
				}
			}
		}
	}

	return columnMulGroupList
}

func (m *m10000004) calculateWins(grid []games.Cell10000004, catMul int16) []games.L10000004 {
	var wins []games.L10000004
	lineID := 1
	checkedIcons := make(map[int16]bool) //记录已经被检查过的图标

	for _, icon := range grid {
		// 跳过夺宝图标, 翻倍图标
		if icon.Symbol == m.Config.FreeSpin.Icon || icon.Symbol == m.Config.MultipleIcon || checkedIcons[icon.Symbol] {
			continue
		}
		checkedIcons[icon.Symbol] = true

		ways, positions, consecutiveCols := m.checkWaysForIcon(grid, icon.Symbol)
		if consecutiveCols >= 3 {
			payouts, exists := m.Config.PayoutTable[icon.Symbol]
			if exists && consecutiveCols-1 < len(payouts) {
				basePayout := payouts[consecutiveCols-1]
				//multiplier := int32(cascadeIndex)*mul + 1
				totalPayout := int32(basePayout) * int32(ways) * int32(catMul)

				fmt.Println("basePayout: ", basePayout, "ways: ", ways, "catMul: ", catMul)

				wins = append(wins, games.L10000004{
					ID:        lineID,
					Positions: positions,       //该图标在grid中所有出现的索引[]int
					Icon:      icon.Symbol,     //中奖的图标
					Count:     consecutiveCols, //连续出现几列
					Value:     totalPayout,     // 总赢奖
					Ways:      ways,            //中奖路
				})
				lineID++
			}
		}
	}
	return wins
}

// 返回: 中奖路, 该图标在grid中所有出现的索引[]int, 连续出现几列
func (m *m10000004) checkWaysForIcon(grid []games.Cell10000004, targetIcon int16) (int, []int, int) {
	ways := 1
	positions := []int{} //这个图标或wild图标 在grid中所有出现的索引
	consecutiveCols := 0 //该图标连续出现几列

	for col := 0; col < m.Config.Column; col++ {
		colMatches := 0         //在本列出现的次数
		colPositions := []int{} //保存这个图标在grid中的索引

		var prevSymbol int16 = 0 //保存当前列的上一行图标数字
		for row := 0; row < m.Config.Row; row++ {
			pos := row*m.Config.Column + col
			icon := grid[pos]

			// 判断是否为被合并的格子
			if icon.Frame > 0 {

				if icon.Symbol == prevSymbol {
					continue
				} else {
					prevSymbol = icon.Symbol
				}
			} else {
				prevSymbol = 0
			}

			if icon.Symbol == targetIcon || (icon.Symbol == m.Config.WildIcon && targetIcon != m.Config.FreeSpin.Icon) {
				colMatches++
				colPositions = append(colPositions, pos)
			}
		}

		if colMatches > 0 {
			ways *= colMatches
			consecutiveCols++
			positions = append(positions, colPositions...)
		} else {
			break
		}
	}
	return ways, positions, consecutiveCols
}

func (m *m10000004) performCascade(grid []games.Cell10000004, wins []games.L10000004, rd *rand.Rand, spin *games.S10000004) ([]games.MobileColumn10000004, []games.Cell10000004) {
	fmt.Println("消除前:")
	for row := 0; row < m.Config.Row; row++ {
		for col := 0; col < m.Config.Column; col++ {
			idx := row*m.Config.Column + col
			fmt.Printf("%v,", grid[idx])
		}
		fmt.Println()
	}

	// 标记需要移除的格子
	toRemove := make(map[int]bool)
	for _, win := range wins {
		for _, pos := range win.Positions {
			toRemove[pos] = true
		}
		// test
		fmt.Print("消除图标:", win.Icon, " 消除的索引:", win.Positions, " ")
		fmt.Print("消除坐标:")
		for _, n := range win.Positions {
			rowindex := n / m.Config.Column
			colindex := n % m.Config.Column
			fmt.Printf("(%d,%d),", rowindex, colindex)
		}
		fmt.Println()
	}

	// 收集被消除的框架信息（仅银框或金框）
	type FrameData struct {
		Col       int
		StartRow  int
		Length    int
		FrameType int16
		Symbol    int16
	}
	var frameData []FrameData
	for col := 0; col < m.Config.Column; col++ {
		for row := 0; row < m.Config.Row; row++ {
			index := row*m.Config.Column + col
			if toRemove[index] && (grid[index].Frame == 1 || grid[index].Frame == 2) {
				startRow := row
				symbol := grid[index].Symbol
				frameType := grid[index].Frame
				length := 1
				r := row + 1
				for r < m.Config.Row {
					nextIndex := r*m.Config.Column + col
					if toRemove[nextIndex] && grid[nextIndex].Frame == frameType && grid[nextIndex].Symbol == symbol {
						length++
						r++
					} else {
						break
					}
				}
				frameData = append(frameData, FrameData{
					Col:       col,
					StartRow:  startRow,
					Length:    length,
					FrameType: frameType,
					Symbol:    symbol,
				})
				row = r - 1
			}
		}
	}

	newGrid := make([]games.Cell10000004, len(grid))
	mobiles := make([]games.MobileColumn10000004, m.Config.Column)

	// 处理每列的下落逻辑
	for col := 0; col < m.Config.Column; col++ {
		colMobiles := []games.Mobile10000004{}
		remainingIcons := []games.Cell10000004{}
		remainingPositions := [][]int{} //收集没被消除的坐标
		removedCount := 0
		var framesInColumn []FrameData

		// 收集该列中未被消除的格子
		for row := 1; row < m.Config.Row; row++ {
			pos := row*m.Config.Column + col
			if !toRemove[pos] {
				remainingIcons = append(remainingIcons, games.Cell10000004{
					Symbol: grid[pos].Symbol,
					Frame:  grid[pos].Frame,
					Mul:    grid[pos].Mul,
				})
				remainingPositions = append(remainingPositions, []int{row, col, pos})
			} else {
				removedCount++
			}
		}

		// 收集该列中被消除的框架
		for _, fd := range frameData {
			if fd.Col == col {
				framesInColumn = append(framesInColumn, fd)
			}
		}

		// 生成新图标用于填充被移除的格子
		var newIcons []games.Cell10000004
		if spin.IsFreeModel {
			if col == 0 || col == m.Config.Column-1 {
				icons32 := m.RandByWeightFreeNoWild.More(removedCount, rd)
				newIcons = make([]games.Cell10000004, len(icons32))
				for i, icon := range icons32 {
					newIcons[i] = games.Cell10000004{Symbol: int16(icon), Frame: 0}
					if newIcons[i].Symbol == m.Config.MultipleIcon { //翻倍图标
						newIcons[i].Mul = m.RandByMultipleWeight.One(rd)
					}
				}
			} else {
				icons32 := m.RandByWeightFree.More(removedCount, rd)
				newIcons = make([]games.Cell10000004, len(icons32))
				for i, icon := range icons32 {
					newIcons[i] = games.Cell10000004{Symbol: int16(icon), Frame: 0}
					if newIcons[i].Symbol == m.Config.MultipleIcon { //翻倍图标
						newIcons[i].Mul = m.RandByMultipleWeight.One(rd)
					}
				}
			}
		} else {
			if col == 0 || col == m.Config.Column-1 {
				icons32 := m.RandByWeightNoWild.More(removedCount, rd)
				newIcons = make([]games.Cell10000004, len(icons32))
				for i, icon := range icons32 {
					newIcons[i] = games.Cell10000004{Symbol: int16(icon), Frame: 0}
					if newIcons[i].Symbol == m.Config.MultipleIcon { //翻倍图标
						newIcons[i].Mul = m.RandByMultipleWeight.One(rd)
					}
				}
			} else {
				icons32 := m.RandByWeight.More(removedCount, rd)
				newIcons = make([]games.Cell10000004, len(icons32))
				for i, icon := range icons32 {
					newIcons[i] = games.Cell10000004{Symbol: int16(icon), Frame: 0}
					if newIcons[i].Symbol == m.Config.MultipleIcon { //翻倍图标
						newIcons[i].Mul = m.RandByMultipleWeight.One(rd)
					}
				}
			}
		}

		// 从底部向上填充新格子，考虑框架的长度
		newIconIndex := 0
		remainingIndex := 0
		currentRow := m.Config.Row - 1

		// 预计算框架占用位置
		frameOccupied := make(map[int]bool)
		for _, fd := range framesInColumn {
			var newFrameType int16
			var newSymbol int16
			// 只有被消除的框架才改变类型
			if fd.FrameType == 1 { // 银框升级为金框
				newFrameType = 2
				newSymbol = m.RandByWeightNoWildAndScatter.One(rd)
			} else if fd.FrameType == 2 { // 金框转换为 Wild
				newFrameType = 0
				newSymbol = m.Config.WildIcon
			} else {
				newFrameType = fd.FrameType
				newSymbol = fd.Symbol
			}

			// 计算框架下方被消除的格子数量
			removedBelow := 0
			for row := fd.StartRow + fd.Length; row < m.Config.Row; row++ {
				if toRemove[row*m.Config.Column+col] {
					removedBelow++
				}
			}

			// 计算框架的新位置（基于下方被消除的格子数量）
			targetRow := fd.StartRow + removedBelow
			if targetRow+fd.Length > m.Config.Row {
				targetRow = m.Config.Row - fd.Length // 确保框架不超出网格
			}
			if targetRow < 0 {
				targetRow = 0
			}

			// 填充框架格子
			for r := targetRow; r < targetRow+fd.Length && r < m.Config.Row; r++ {
				if frameOccupied[r] {
					continue // 跳过已占用的位置
				}
				index := r*m.Config.Column + col
				newGrid[index] = games.Cell10000004{Symbol: newSymbol, Frame: newFrameType}
				frameOccupied[r] = true
				colMobiles = append(colMobiles, games.Mobile10000004{
					Card:  int(newSymbol),
					Row:   fd.StartRow + 1,
					Col:   col + 1,
					Type:  1,
					Trow:  r + 1,
					Tcol:  col + 1,
					Frame: newFrameType,
				})
			}
			currentRow = utils.Min(currentRow, targetRow-1) // 更新当前行，防止格子填充到框架上方
		}

		// 填充剩余的格子，从底部向上
		availableRows := make([]int, 0)
		for row := m.Config.Row - 1; row >= 1; row-- {
			if !frameOccupied[row] {
				availableRows = append(availableRows, row)
			}
		}

		for _, row := range availableRows {
			targetPos := row*m.Config.Column + col
			//从下向上填充
			if remainingIndex < len(remainingIcons) {
				newGrid[targetPos] = remainingIcons[len(remainingIcons)-1-remainingIndex]
				beforeposition := remainingPositions[len(remainingIcons)-1-remainingIndex]

				if beforeposition[2] != targetPos {
					colMobiles = append(colMobiles, games.Mobile10000004{
						Card: int(newGrid[targetPos].Symbol),
						//Row:   origRow + 1,
						Row:   beforeposition[0] + 1,
						Col:   col + 1,
						Type:  1,
						Trow:  row + 1,
						Tcol:  col + 1,
						Frame: newGrid[targetPos].Frame,
					})
				}
				remainingIndex++
			} else if newIconIndex < len(newIcons) {
				newGrid[targetPos] = newIcons[newIconIndex]
				colMobiles = append(colMobiles, games.Mobile10000004{
					Card:  int(newIcons[newIconIndex].Symbol),
					Row:   -1,
					Col:   -1,
					Type:  2,
					Trow:  row + 1,
					Tcol:  col + 1,
					Frame: 0,
				})
				newIconIndex++
			}
		}

		mobiles[col] = games.MobileColumn10000004{
			Mobiles: colMobiles,
			Col:     col + 1,
		}

	}

	// 处理第一行的逻辑
	{
		colMobiles2 := []games.Mobile10000004{}
		remainingIcons2 := []games.Cell10000004{} //收集没被消除的格子
		remainingPositions2 := [][]int{}          //收集没被消除的坐标
		removedCount2 := 0
		//var framesInColumn2 []FrameData

		// 收集第一行未被消除的格子
		for col := 1; col < m.Config.Column-1; col++ {
			if !toRemove[col] {
				remainingIcons2 = append(remainingIcons2, games.Cell10000004{
					Symbol: grid[col].Symbol,
					Frame:  grid[col].Frame,
					Mul:    grid[col].Mul,
				})
				remainingPositions2 = append(remainingPositions2, []int{0, col, col})
			} else {
				removedCount2++
			}
		}
		// 生成新图标用于填充被移除的格子
		var newIcons2 []games.Cell10000004
		if spin.IsFreeModel {
			icons32 := m.RandByWeightFree.More(removedCount2, rd)
			newIcons2 = make([]games.Cell10000004, len(icons32))
			for i, icon := range icons32 {
				newIcons2[i] = games.Cell10000004{Symbol: int16(icon), Frame: 0}
				if newIcons2[i].Symbol == m.Config.MultipleIcon { //翻倍图标
					newIcons2[i].Mul = m.RandByMultipleWeight.One(rd)
				}
			}
		} else {
			icons32 := m.RandByWeight.More(removedCount2, rd)
			newIcons2 = make([]games.Cell10000004, len(icons32))
			for i, icon := range icons32 {
				newIcons2[i] = games.Cell10000004{Symbol: int16(icon), Frame: 0}
				if newIcons2[i].Symbol == m.Config.MultipleIcon { //翻倍图标
					newIcons2[i].Mul = m.RandByMultipleWeight.One(rd)
				}
			}
		}

		// 从右往左填充新格子
		newIconIndex2 := 0
		remainingIndex2 := 0
		//currentCol := m.Config.Column - 2

		availableCols := make([]int, 0)
		//for i := m.Config.Column - 2; i >= 1; i-- {
		//	availableCols = append(availableCols, i)
		//}
		for i := 1; i < m.Config.Column-1; i++ {
			availableCols = append(availableCols, i)
		}

		//fmt.Println("availableCols", availableCols)
		//fmt.Println("第1行没被消除的格子:remainingIcons2->", remainingIcons2)
		for _, col := range availableCols {
			targetPos := col
			if remainingIndex2 < len(remainingIcons2) {
				//newGrid[targetPos] = remainingIcons2[len(remainingIcons2)-1-remainingIndex2]
				newGrid[targetPos] = remainingIcons2[remainingIndex2]
				//fmt.Println("targetPos::", targetPos)
				//origCol := m.Config.Column - 2 - remainingIndex2
				beforeposition := remainingPositions2[remainingIndex2]
				if beforeposition[2] != targetPos {
					colMobiles2 = append(colMobiles2, games.Mobile10000004{
						Card:  int(newGrid[targetPos].Symbol),
						Row:   1,
						Col:   beforeposition[1] + 1,
						Type:  1,
						Trow:  1,
						Tcol:  col + 1,
						Frame: newGrid[targetPos].Frame,
					})
				}
				remainingIndex2++
			} else if newIconIndex2 < len(newIcons2) {
				newGrid[targetPos] = newIcons2[newIconIndex2]
				colMobiles2 = append(colMobiles2, games.Mobile10000004{
					Card:  int(newIcons2[newIconIndex2].Symbol),
					Row:   -1,
					Col:   -1,
					Type:  1,
					Trow:  1,
					Tcol:  col + 1,
					Frame: 0,
				})
				newIconIndex2++
			}
		}

		//mobiles = append(mobiles, games.MobileColumn10000004{
		//	Mobiles: colMobiles2,
		//	Col:     -1,
		//})
		mobiles = append([]games.MobileColumn10000004{
			games.MobileColumn10000004{
				Mobiles: colMobiles2,
				Col:     -1,
			},
		}, mobiles...)
	}

	for _, mobile := range mobiles {
		fmt.Printf("%+v\n", mobile)
	}

	fmt.Println("消除后:")
	for row := 0; row < m.Config.Row; row++ {
		for col := 0; col < m.Config.Column; col++ {
			idx := row*m.Config.Column + col
			fmt.Printf("%v,", newGrid[idx])
		}
		fmt.Println()
	}

	return mobiles, newGrid
}

func (m *m10000004) checkScatters(grid []games.Cell10000004, spin *games.S10000004, rd *rand.Rand) []map[string]any {
	scatterList := []map[string]any{}
	scatterCount := 0
	scatterPositions := []int{}

	for gridIndex, gridValue := range grid {
		if gridValue.Symbol == m.Config.FreeSpin.Icon {
			scatterCount++
			scatterPositions = append(scatterPositions, gridIndex)
		}
	}

	if scatterCount >= m.Config.FreeSpin.MinCount && !spin.IsStartFree {
		spin.IsStartFree = true
		spin.IsFreeModel = true
		spin.FreeSpins = int32(m.Config.FreeSpin.Count)
		spin.Multiplier = 1
		for _, gridIndex := range scatterPositions {
			var extraSpins int
			var extraMultiplier int
			reward := m.RandByWeightFreeSpin.One(rd)

			switch reward {
			case 1:
				extraSpins = 1
				extraMultiplier = 0
				spin.FreeSpins += int32(extraSpins)
			case 2:
				extraSpins = 2
				extraMultiplier = 0
				spin.FreeSpins += int32(extraSpins)
			case 3:
				extraSpins = 3
				extraMultiplier = 0
				spin.FreeSpins += int32(extraSpins)
			case 4:
				extraMultiplier = 1
				spin.Multiplier += int32(extraMultiplier)
			}

			row := gridIndex / m.Config.Column
			col := gridIndex % m.Config.Column
			scatterList = append(scatterList, map[string]any{
				"Point": map[string]any{
					"x": row + 1,
					"y": col + 1,
				},
				"Mul":   extraMultiplier,
				"Count": extraSpins,
			})
		}
	} else if spin.IsFreeModel {
		for _, gridIndex := range scatterPositions {
			spin.FreeSpins += 1 // Increment free spins for each Scatter
			row := gridIndex / m.Config.Column
			col := gridIndex % m.Config.Column
			scatterList = append(scatterList, map[string]any{
				"Point": map[string]any{
					"x": row,
					"y": col,
				},
				"Mul":   0,
				"Count": 1,
			})
		}
	} else {
		for _, gridIndex := range scatterPositions {
			row := gridIndex / m.Config.Column
			col := gridIndex % m.Config.Column
			scatterList = append(scatterList, map[string]any{
				"Point": map[string]any{
					"x": row + 1,
					"y": col + 1,
				},
				"Mul":   0,
				"Count": 0,
			})
		}
	}
	return scatterList
}

func (m *m10000004) ZeroSpin(ctl int32, rd *rand.Rand) basic.ISpin {
	for {
		spin := m.Spin(rand.New(rd))
		if spin.Payout() == 0 {
			return spin
		}
	}
}

func (m *m10000004) Salting(spin basic.ISpin, salt *rand.Rand) basic.ISpin {
	return spin
}

func (m10000004) Rule() string {
	return ""
}

func (m10000004) InputCoef(ctl int32) int32 {
	switch ctl {
	default:
		return 100
	case 1:
		return 10000
	}
}

func (m m10000004) MinPayout(ctl int32) int32 {
	return 0
}
