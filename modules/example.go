package modules

import (
	"encoding/json"
	"fmt"
	"igameCommon/basic"
	"igameCommon/utils"
	"log"
	"math/rand"
)

// Template

var _ = Factory.reg(defaultCreate[*m000000]) // 注册

type m000000 struct {
	config c000000
}

func (m *m000000) Generate(configs []byte) {
	m.config = utils.ParseYAML[c000000](configs)
}

func (m *m000000) ID() int32 {
	return 0
}

func (m m000000) Line() int32 { // 线数
	return 100
}

func (m m000000) ClientMode() int32 {
	return basic.EnumClientMode.ONE
}

func (m m000000) Exception(code int32) string {
	return "{}"
}

func (m *m000000) ZeroSpin(ctl int32, seed int64) basic.ISpin {
	return s000000{}
}

func (m *m000000) MinSpin(ctl int32, seed int64) basic.ISpin {
	return s000000{}
}

func (m *m000000) Spin(spinID int64) basic.ISpin {
	return s000000{}
}

func (m *m000000) Salting(spin basic.ISpin, seed int64) basic.ISpin {
	return spin
}

func (m *m000000) PayTable() map[int64]basic.ISpin {
	return map[int64]basic.ISpin{}
}

func (m m000000) Rule() string {
	b, _ := json.Marshal(m.config)
	return string(b)
}

func (m m000000) InputCoef(int32) int32 {
	return 100
}

func (m m000000) MinPayout(int32) int32 {
	return 0
}

type c000000 struct {
	GameID    int   // 游戏ID
	RuleID    int   // 生成器规则ID
	Row       int32 // 网格行数
	Column    int32 // 网格列数
	MaxPayout int32
}

// Example General

var _ = Factory.reg(basic.NewGeneral[*g000000])

type g000000 struct {
	Config c000000
}

func (g *g000000) Init(config []byte) {
	g.Config = utils.ParseYAML[c000000](config)
	log.Print(g.Config.MaxPayout)
}

func (g000000) ID() int32 {
	return 100
}

func (m g000000) Line() int32 { // 线数
	return 20
}

func (m g000000) ClientMode() int32 {
	return basic.EnumClientMode.ONE
}

func (m g000000) Exception(code int32) string {
	return "{}"
}

func (g *g000000) Spin(rd *rand.Rand) basic.ISpin {
	n := rd.Int31n(100000)
	if n >= 99990 {
		return s000000{
			Pays: n,
		}
	}
	return s000000{
		Pays: 0,
	}
}

func (g *g000000) ZeroSpin(ctl int32, rd *rand.Rand) basic.ISpin {
	n := rd.Int31n(100000)
	if n >= 99990 {
		return s000000{
			Pays: n,
		}
	}
	return s000000{
		Pays: 0,
	}
}

func (g *g000000) Salting(spin basic.ISpin, salt *rand.Rand) basic.ISpin {
	n := salt.Int31n(100000)
	if n >= 99990 {
		return s000000{
			Pays: n,
		}
	}
	return s000000{
		Pays: 0,
	}
}

func (g000000) Rule() string {
	return ""
}

func (g000000) InputCoef(int32) int32 {
	return 100
}

func (g000000) MinPayout(int32) int32 {
	return 0
}

type s000000 struct {
	Pays int32
}

func (s s000000) Payout() int32 {
	return s.Pays
}

func (s s000000) Exception(code int32) string {
	return ""
}

func (s s000000) RoundPayouts() []int32 {
	return []int32{s.Pays}
}

func (s s000000) Data(ctx basic.SpinContext) string {
	return fmt.Sprint(s.Pays)
}

func (s s000000) RoundData(index int32, ctx basic.SpinContext) string {
	return s.Data(ctx)
}

func (s s000000) Tag() string {
	return "#"
}

func (s s000000) Tags() []string {
	return []string{s.Tag()}
}
