package modules

import (
	"fmt"
	"igame/basic"
	"igame/utils"
	"math/rand"
	"strings"
	"time"
)

type c200001 struct {
	GameID      int                 // 游戏ID
	RuleID      int                 // 生成器规则ID
	Row         int32               // 网格行数
	Column      int32               // 网格列数
	MaxPayout   int32               // 最大支付
	PayoutTable map[int16][]float64 // 支付表，key 为图标 ID，value 为对应的支付数组
	IconWeight  map[int16]int       // 图标权重
}

var _ = Factory.reg(basic.NewGeneral[*m200001])

type m200001 struct {
	Config       c200001
	RandByWeight *utils.RandomWeightPicker[int16, int]
}

func (m *m200001) Init(config []byte) {
	m.Config = utils.ParseYAML[c200001](config)
	m.RandByWeight = utils.NewRandomWeightPicker(m.Config.IconWeight)
}

func (m200001) ID() int32 {
	return 200001
}

func (m m200001) Line() int32 { // 线数
	return 20
}

func (m m200001) ClientMode() int32 {
	return basic.EnumClientMode.MULTI
}

func (m m200001) Exception(code int32) string {
	return "{}"
}

// 生成初始网格
func (m *m200001) genGrid(rd *rand.Rand) []int16 {
	grid := make([]int16, m.Config.Row*m.Config.Column)
	grid = m.RandByWeight.More(int(m.Config.Row*m.Config.Column), rd)
	return grid
}

// 执行级联消除
func (m *m200001) performCascade(grid []int16, rd *rand.Rand, spin *s200001) ([]int16, []l200001) {
	newGrid := make([]int16, len(grid))
	copy(newGrid, grid)

	lines := m.parsePayout(newGrid, spin)

	// 移除获胜符号
	removed := make(map[int]bool)
	for _, line := range lines {
		for _, pos := range line.Positions {
			removed[pos] = true
		}
	}

	// 更新网格：移除的符号置为 0，新符号掉落
	for col := 0; col < int(m.Config.Column); col++ {
		newCol := make([]int16, 0, m.Config.Row)
		// 收集非零符号（从下往上）
		for row := int(m.Config.Row) - 1; row >= 0; row-- {
			idx := row*int(m.Config.Column) + col
			if !removed[idx] {
				newCol = append(newCol, newGrid[idx])
			}
		}
		// 用新符号填充空位
		for len(newCol) < int(m.Config.Row) {
			newCol = append(newCol, m.RandByWeight.One(rd))
		}
		// 倒序填回网格（从下往上）
		for row := int(m.Config.Row) - 1; row >= 0; row-- {
			idx := row*int(m.Config.Column) + col
			newGrid[idx] = newCol[int(m.Config.Row)-1-row]
		}
	}
	return newGrid, lines
}

// 解析支付信息
func (m *m200001) parsePayout(grid []int16, spin *s200001) []l200001 {
	iconCounts := make(map[int16]int)
	iconPositions := make(map[int16][]int)
	lines := make([]l200001, 0)
	id := 0

	// 统计每个 icon 的出现次数和位置
	for i, icon := range grid {
		iconCounts[icon]++
		iconPositions[icon] = append(iconPositions[icon], i)
	}

	// 处理每个 icon 的 payout
	for icon, count := range iconCounts {
		// 检查 PayoutTable 中是否存在该 icon 和对应的 count
		if payoutData, exists := m.Config.PayoutTable[icon]; exists && count >= 8 || icon == 1 {
			payout := payoutData[count]
			if payout > 0 {
				if spin.IsFree {
					spin.FreeSpin += 5
				} else {
					// 进入免费模式
					if icon == 1 {
						spin.IsFree = true
						spin.FreeSpin = 10
					}
				}
				// 计算倍率
				spin.Pays += int32(payout)
				lines = append(lines, l200001{
					ID:        id,
					Positions: iconPositions[icon], // 使用正确的位置坐标
					Icon:      icon,
					Count:     int(count),
					Value:     float64(payout),
				})
				id++
			}
		}
	}
	return lines
}

// 执行旋转逻辑
func (m *m200001) Spin(rd *rand.Rand) basic.ISpin {
	spin := &s200001{
		Pages:  []*p200001{},
		Rounds: []*s200001{},
	}

	// 生成初始网格并执行级联消除
	grid := m.genGrid(rd)
	for {
		page := &p200001{
			Grid:  make([]int16, len(grid)),
			Lines: []l200001{},
		}
		copy(page.Grid, grid)
		newGrid, lines := m.performCascade(grid, rd, spin)
		if len(lines) == 0 {
			spin.Pages = append(spin.Pages, page)
			break
		}
		page.Lines = lines
		for _, line := range lines {
			page.Pay += line.Value
		}
		spin.Pages = append(spin.Pages, page)
		grid = newGrid
	}

	// 如果触发免费旋转，生成免费旋转轮次
	if spin.IsFree {
		for i := 0; i < int(spin.FreeSpin); i++ {
			freeSpin := &s200001{
				Pages:  []*p200001{},
				IsFree: true,
			}
			grid := m.genGrid(rd)
			for {
				page := &p200001{
					Grid:  make([]int16, len(grid)),
					Lines: []l200001{},
				}
				copy(page.Grid, grid)
				newGrid, lines := m.performCascade(grid, rd, freeSpin)
				if len(lines) == 0 {
					freeSpin.Pages = append(freeSpin.Pages, page)
					break
				}
				page.Lines = lines
				for _, line := range lines {
					page.Pay += line.Value
				}
				freeSpin.Pages = append(freeSpin.Pages, page)
				grid = newGrid
			}
			spin.Rounds = append(spin.Rounds, freeSpin)
		}
	}

	return spin
}

func (m *m200001) ZeroSpin(ctl int32, rd *rand.Rand) basic.ISpin {
	n := rd.Int31n(100000)
	if n >= 99990 {
		return s200001{
			Pays: n,
		}
	}
	return s200001{
		Pays: 0,
	}
}

func (m *m200001) Salting(spin basic.ISpin, salt *rand.Rand) basic.ISpin {
	n := salt.Int31n(100000)
	if n >= 99990 {
		return s200001{
			Pays: n,
		}
	}
	return s200001{
		Pays: 0,
	}
}

func (m200001) Rule() string {
	return ""
}

func (m200001) InputCoef(ctl int32) int32 {
	switch ctl {
	case 1:
		return 10000
	default:
		return 100
	}
}

type s200001 struct {
	Pays     int32
	Pages    []*p200001
	Rounds   []*s200001
	IsFree   bool
	FreeSpin int32
}

type p200001 struct {
	Grid       []int16
	Pay        float64
	Lines      []l200001
	Multiplier int32
	FreeSpins  int32
}

type l200001 struct {
	ID        int
	Positions []int
	Icon      int16
	Count     int
	Value     float64
}

func (s s200001) Payout() int32 {
	return s.Pays
}

func (s s200001) RoundPayouts() []int32 {
	pays := []int32{}
	if len(s.Rounds) == 0 {
		for _, page := range s.Pages {
			pays = append(pays, int32(page.Pay))
		}
	} else {
		for _, round := range s.Rounds {
			for _, page := range round.Pages {
				pays = append(pays, int32(page.Pay))
			}
		}
	}
	return pays
}

// 生成响应数据
func (s s200001) Data(ctx basic.SpinContext) string {
	index := ctx.Int32("index")
	data := [][]byte{} // 预分配空间，包含所有页面、最后旋转和完成数据
	balance := ctx.Float64("balance")
	win := 0.00
	fmt.Println("index", index)

	// 生成所有轮次的响应数据
	if s.IsFree {
		for i, round := range s.Rounds {
			for j, page := range round.Pages {
				if j == len(round.Pages)-1 {
					break // 最后一页不需要生成数据
				}
				result := map[string]any{
					"s":               utils.IntArrayToString(page.Grid),
					"rs_c":            1,
					"rs_m":            1,
					"rs_p":            0,
					"stime":           time.Now().UnixMilli(),
					"index":           index,
					"reel_set":        0,
					"rs_iw":           400, // 初始下注
					"rid":             "1932267909616005120",
					"balance_cash":    balance,
					"balance_bonus":   0.00,
					"counter":         348,
					"l":               20,
					"small_game_type": 2,
					"ntp":             0.00,
					"w":               page.Pay,
					"game_multiple":   10000,
					"na":              "s",
					"sver":            5,
					"sa":              utils.IntArrayToString([]int16{5, 1, 7, 5, 9, 4, 1}),
					"sb":              utils.IntArrayToString([]int16{9, 8, 5, 1, 5, 8, 4}),
					"bl":              0,
					"balance":         balance,
					"sw":              6,
					"sh":              5,
					"c":               50,
					"tw":              win + page.Pay,
					"fsmax":           s.FreeSpin,
					"fsmul":           1,
					"fs":              i + 1,
				}

				// 处理支付线
				mark := make([]string, 0)
				for j, line := range page.Lines {
					var pos strings.Builder
					for _, p := range line.Positions {
						pos.WriteString(fmt.Sprintf("%d~", p))
						mark = append(mark, fmt.Sprintf("%d:%d", line.Icon, p))
					}
					result[fmt.Sprintf("l%d", j)] = fmt.Sprintf("%d~%.2f~%s", line.ID, line.Value, pos.String()[:len(pos.String())-1])
					result["s_mark"] = "tmb~" + strings.Join(mark, ",")
				}

				win += page.Pay
				data = append(data, []byte(map2FormString(result)))
			}
			lastSpin := map[string]any{
				"na":              "c",
				"index":           index,
				"sa":              "5,1,7,5,9,4,1",
				"sh":              "5",
				"game_multiple":   "10000",
				"balance":         "79150.00",
				"balance_cash":    "79150.00",
				"sver":            "5",
				"ntp":             "0.00",
				"l":               "20",
				"c":               "50",
				"s":               utils.IntArrayToString(s.Pages[len(s.Pages)-1].Grid),
				"balance_bonus":   "0.00",
				"tw":              "0",
				"sb":              "9,8,5,1,5,8,4",
				"small_game_type": "1",
				"rid":             "1932376829764333568",
				"stime":           "1749549512701",
				"counter":         "26",
				"w":               "0",
				"reel_set":        "0",
				"sw":              "6",
				"ffffinish":       "true",
				"rs_t":            len(s.Pages) - 1,
				"bl":              "0",
				"fsmax":           s.FreeSpin,
				"fsmul":           1,
				"fs":              i + 1,
			}
			data = append(data, []byte(map2FormString(lastSpin)))
		}
	} else {
		for i, page := range s.Pages {
			if i == len(s.Pages)-1 {
				break // 最后一页不需要生成数据
			}
			result := map[string]any{
				"s":               utils.IntArrayToString(page.Grid),
				"rs_c":            1,
				"rs_m":            1,
				"rs_p":            0,
				"stime":           time.Now().UnixMilli(),
				"index":           index,
				"reel_set":        0,
				"rs_iw":           400, // 初始下注
				"rid":             "1932267909616005120",
				"balance_cash":    balance,
				"balance_bonus":   0.00,
				"counter":         348,
				"l":               20,
				"small_game_type": 0,
				"ntp":             0.00,
				"w":               page.Pay,
				"game_multiple":   10000,
				"na":              "s",
				"sver":            5,
				"sa":              utils.IntArrayToString([]int16{5, 1, 7, 5, 9, 4, 1}),
				"sb":              utils.IntArrayToString([]int16{9, 8, 5, 1, 5, 8, 4}),
				"bl":              0,
				"balance":         balance,
				"sw":              6,
				"sh":              5,
				"c":               50,
				"tw":              win + page.Pay,
			}

			// 处理支付线
			mark := make([]string, 0)
			for j, line := range page.Lines {
				var pos strings.Builder
				for _, p := range line.Positions {
					pos.WriteString(fmt.Sprintf("%d~", p))
					mark = append(mark, fmt.Sprintf("%d:%d", line.Icon, p))
				}
				result[fmt.Sprintf("l%d", j)] = fmt.Sprintf("%d~%.2f~%s", line.ID, line.Value, pos.String()[:len(pos.String())-1])
				result["s_mark"] = "tmb~" + strings.Join(mark, ",")
			}

			win += page.Pay
			data = append(data, []byte(map2FormString(result)))
		}
		lastSpin := map[string]any{
			"na":              "c",
			"index":           index,
			"sa":              "5,1,7,5,9,4,1",
			"sh":              "5",
			"game_multiple":   "10000",
			"balance":         "79150.00",
			"balance_cash":    "79150.00",
			"sver":            "5",
			"ntp":             "0.00",
			"l":               "20",
			"c":               "50",
			"s":               utils.IntArrayToString(s.Pages[len(s.Pages)-1].Grid),
			"balance_bonus":   "0.00",
			"tw":              "0",
			"sb":              "9,8,5,1,5,8,4",
			"small_game_type": "1",
			"rid":             "1932376829764333568",
			"stime":           "1749549512701",
			"counter":         "26",
			"w":               "0",
			"reel_set":        "0",
			"sw":              "6",
			"ffffinish":       "true",
			"rs_t":            len(s.Pages) - 1,
			"bl":              "0",
		}
		data = append(data, []byte(map2FormString(lastSpin)))
	}

	// 根据 index 返回对应轮次的数据
	return string(data[index])
}

func (s s200001) RoundData(index int32, ctx basic.SpinContext) string {
	ctx["index"] = index // 将 index 存入 ctx
	if index > int32(len(s.RoundPayouts())-1) {
		delete(ctx, "spinID")
	}
	return s.Data(ctx)
}

func (s s200001) Tag() string {
	if len(s.Rounds) > 0 {
		return basic.EnumSpinTag.FREE
	} else {
		return basic.EnumSpinTag.NORM
	}
}

func (s s200001) Tags() []string {
	return []string{s.Tag()}
}

func map2FormString(m map[string]any) string {
	parts := []string{}
	for k, v := range m {
		parts = append(parts, fmt.Sprintf("%s=%v", k, v))
	}
	return strings.Join(parts, "&")
}
