配置说明:
configs下
10000x.yaml为规则配置
10000xCTLy.yaml为最终返奖分布配置
simulate.temp.yaml为统计的配置模版, 使用时需要复制粘贴成simulate.yaml

步骤:
1. 按程序的设定修改10000x.yaml
2. 运行shell/gen.cmd, 数据生成后除非改代码或者改配置, 否则不用在执行gen
3. 根据需要调整10000xCTLy.yaml文件, 然后运行shell/paytab.cmd, 如果存在错误按错误提示修改, 不会解决直接找程序提问
4. 如果需要模拟, 修改configs/simulate.yaml文件(从simulate.temp.yaml复制粘贴), 然后运行simulate.cmd
   输出的文件在logs文件夹中, 以参数信息构成文件名
5. 觉得没问题, git提交generated文件夹的变化的文件和configs下你修改的文件, 结束