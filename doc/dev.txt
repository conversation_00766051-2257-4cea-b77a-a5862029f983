每新增个游戏只需要在modules文件夹下新增一个文件, 实现一个IModule接口的struct, 其他的一切自动处理
每个游戏数据和方法全部集成在一个IModule上, IModule定义在modules/factory.go文件里
每个游戏的配置在configs路径下, YAML格式, 在调用IModule.Generate时会自动传入
每次新增游戏, 只需要新增一个m100xxx.go文件和程序自动生成的文件

生成流程:
1. 调用IModule.Generate方法传入定义好的配置, Generate里根据配置生成全部数据, 并将需要的数据存在IModule上
2. 程序会自动将IModule数据快照保存成文件(序列化之后压缩然后base64编码成字符串存储成go文件), struct大写字段才会被保存
3. 调用IModule.PayTable, 调用外部算法得到HashID到SpinID的对应关系并存储, Payout是百分比或者说是两位小数点的定点数

调用流程:
1. 从保存的文件中读取快照数据并还原, 得到Generate执行完之后的IModule
2. 根据Hash算法得到一个HashID, 查表得到HashID对应的SpinID
3. 如果有奖励调用IModule.Spin得到中奖的结果; 如果无奖励, 调用IModule.ZeroSpin生成一个无奖的结果
   Spin和ZeroSpin返回的是一个ISpin接口, 其中Data方法返回的数据就是发送给客户端的数据, 

注意事项:
1. 参考test文件夹下其他写一个测试用例, 每次游戏数据生成完, 把所有结果全部调用保证Spin时不保存
2. 生成的代码要保证结果的可重复性, 同一份代码配置运行两遍结果一样, 这里判断的标准是generated的paytab文件不发生改变
3. 控制结果的占用内存的大小和存储文件的大小, 这会影响到最终的内存占用和服务器编译二进制文件大小
4. 避免Generate执行时间过长, 遇到性能问题先只生成少量数据运行完程序, 通过make pprof指令分析原因

快捷指令:
生成数据: make gen id=100001
测试数据: make test id=100001
开启测试接口: make dev id=100001, 请求端口9528