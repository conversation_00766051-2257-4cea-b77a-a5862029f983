# UPX安装脚本
Write-Host "开始下载UPX..." -ForegroundColor Green

# 设置下载参数
$ProgressPreference = 'SilentlyContinue'
$upxUrl = "https://github.com/upx/upx/releases/download/v4.2.1/upx-4.2.1-win64.zip"
$zipFile = "upx.zip"
$extractPath = "tools"

# 创建tools目录
if (!(Test-Path $extractPath)) {
    New-Item -ItemType Directory -Path $extractPath | Out-Null
}

# 下载UPX
Write-Host "正在下载UPX..." -ForegroundColor Yellow
Invoke-WebRequest -Uri $upxUrl -OutFile $zipFile

# 解压文件
Write-Host "正在解压UPX..." -ForegroundColor Yellow
Expand-Archive -Path $zipFile -DestinationPath $extractPath -Force

# 移动upx.exe到tools目录
$upxExe = Get-ChildItem -Path $extractPath -Recurse -Filter "upx.exe" | Select-Object -First 1
if ($upxExe) {
    Move-Item -Path $upxExe.FullName -Destination (Join-Path $extractPath "upx.exe") -Force
}

# 清理下载的zip文件
Remove-Item $zipFile -Force

Write-Host 'UPX installation complete!' -ForegroundColor Green
Write-Host 'UPX path: tools\upx.exe' -ForegroundColor Cyan
Write-Host 'Usage: .\tools\upx.exe --help' -ForegroundColor Cyan