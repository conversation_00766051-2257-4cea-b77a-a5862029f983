package main

import (
	"bytes"
	"encoding/gob"
	"fmt"
	"igame"
	"igameCommon/basic"
	"igameCommon/utils"
	"log"
	"math"
	"os"
	"os/exec"
	"slices"
	"strconv"
	"strings"
	"time"
)

var (
	m basic.IModule
)

func main() {
	utils.Init()
	st := time.Now()
	gameID, err := strconv.Atoi(os.Args[1])
	if err != nil {
		panic(err)
	}
	ctlID, err := strconv.Atoi(os.Args[2])
	if err != nil {
		panic(err)
	}
	m = igame.GetModuleFromSnapshoot(int32(gameID))
	generateCSV(gameID, ctlID)
	generateGo(gameID, ctlID)
	log.Printf("create paytable completed, total elapsed time %v\n", time.Since(st))
}

func parseCSV(b []byte) [][]string {
	var result [][]string
	txt := string(b)
	strs := strings.Split(txt, "\n")
	strs = strs[1:]
	for _, str := range strs {
		str = strings.TrimSpace(str)
		if len(str) == 0 {
			continue
		}
		items := strings.Split(str, ",")
		for i := 0; i < len(items); i++ {
			items[i] = strings.TrimSpace(items[i])
		}
		result = append(result, items)
	}
	return result
}

func generateCSV(gameID int, ctl int) {
	lines := m.Line()
	coef := float64(m.InputCoef(int32(ctl))) / 100
	var conf = map[string]map[float64][]float64{}
	if _, err := os.Stat(fmt.Sprintf("configs/%dCTL%d.csv", gameID, ctl)); err == nil {
		b, err := os.ReadFile(fmt.Sprintf("configs/%dCTL%d.csv", gameID, ctl))
		if err != nil {
			panic(err)
		}
		data := utils.ParseCSV[struct {
			Tag        string
			Payout     float64
			Probablity float64
			Reward     float64
		}](b)
		for _, item := range data {
			if conf[item.Tag] == nil {
				conf[item.Tag] = map[float64][]float64{}
			}
			conf[item.Tag][item.Payout] = []float64{item.Probablity, item.Reward}
		}
	} else {
		b, err := os.ReadFile(fmt.Sprintf("configs/%dCTL%d.yaml", gameID, ctl))
		if err != nil {
			panic(err)
		}
		conf = utils.ParseYAML[map[string]map[float64][]float64](b)
	}
	var probability float64
	for _, item := range conf {
		for _, value := range item {
			probability += value[0]
		}
	}
	if probability > 100 {
		log.Fatalf("ERR! The sum of the probabilities must be less than 100")
	}
	b, err := os.ReadFile(fmt.Sprintf("generated/data%d.csv", gameID))
	if err != nil {
		panic(err)
	}
	err = os.WriteFile("bin/data.csv", b, 0755)
	if err != nil {
		panic(err)
	}
	csv := parseCSV(b)

	count := map[string]int{}
	minv := map[string]float64{}
	for _, items := range csv {
		n, err := strconv.Atoi(strings.TrimSpace(items[1]))
		if err != nil {
			panic(err)
		}
		tag := items[2]
		if tag == "#" {
			tag = basic.EnumSpinTag.NORM
		}
		pay := float64(n) / float64(lines)
		upper := match(pay, conf[tag])

		key := fmt.Sprintf("%s %s", items[2], stringf(upper))
		count[key]++
		if minv[key] == 0 || minv[key] > pay {
			minv[key] = pay
		}
	}
	data := string(b)
	strs := []string{`%f:max,%f:probablity%,%f:reward%,%s:fcc`}
	tags := utils.MapKeysOrdered(conf)
	slices.SortFunc(tags, func(a, b string) int {
		return strings.Index(data, a) - strings.Index(data, b)
	})
	if index := slices.Index(tags, basic.EnumSpinTag.NORM); index != -1 {
		tags = slices.Delete(tags, index, index+1)
		tags = append(tags, basic.EnumSpinTag.NORM)
	}
	for _, tag := range tags {
		items := conf[tag]
		if tag == basic.EnumSpinTag.NORM {
			tag = "#"
		}
		pays := utils.MapKeysOrdered(items)
		slices.Reverse(pays)
		for i, pay := range pays {
			var lastPay float64 = 0
			if i < len(pays)-1 {
				lastPay = pays[i+1]
			}
			item := items[pay]
			mean := item[1] / item[0]
			var (
				low  = (pay-lastPay)*0.1 + lastPay
				high = (pay + lastPay) / 2
			)
			tag0 := tag
			if tag0 == "#" {
				tag0 = basic.EnumSpinTag.NORM
			}
			if mean < low || mean > high {
				log.Fatalf("ERR! [%s %s] can not be solved, mean: %f must in [%f, %f]", tag0, stringf(pay), mean, low, high)
			}
			key := fmt.Sprintf("%s %s", tag, stringf(pay))
			if count[key] == 0 && item[0] > 0 {
				log.Fatalf("ERR! %s %s empty", tag0, stringf(pay))
			}
			if minv[key] >= mean {
				log.Fatalf("ERR! [%s %s] can not be solved, minimum value is [%v]", tag0, stringf(pay), minv[key])
			}
			strs = append(strs, fmt.Sprintf("%s,%.10f,%.10f,%s", stringf(pay), item[0], item[1], tag))
		}
	}
	text := strings.Join(strs, "\n")
	f, err := os.Create("bin/ctl.csv")
	if err != nil {
		panic(err)
	}
	f.Write([]byte(text))
	f.Close()
	log.Println("The CSV processing is complete, awaiting xterns results...")
	os.Remove("./bin/game.csv")
	//coef 目标返奖率
	cmd := exec.Command("./bin/xterns.exe", fmt.Sprint(float64(lines)), stringf(coef))
	cmd.Dir, _ = os.Getwd()
	cmd.Stdout = os.Stdout // 标准输出直接打印到控制台
	cmd.Stderr = os.Stderr // 错误输出直接打印到控制台
	cmd.Run()
	// fmt.Println(cmd.Output())
	// exec.Command("echo", fmt.Sprint(lines)).Run()
}

func generateGo(gameID int, ctl int) {
	b, err := os.ReadFile("bin/game.csv")
	if err != nil {
		panic(err)
	}
	txt := string(b)
	strs := strings.Split(txt, "\n")
	strs = strs[1:]
	table := [][2]uint32{}
	var total float64
	var last int
	for _, str := range strs {
		str = strings.TrimSpace(str)
		if len(str) == 0 {
			continue
		}
		items := strings.Split(str, ",")[:3]
		a, err := strconv.Atoi(strings.TrimSpace(items[0]))
		if err != nil {
			panic(err)
		}
		b, err := strconv.Atoi(strings.TrimSpace(items[1]))
		if err != nil {
			panic(err)
		}
		c, _ := strconv.Atoi(strings.TrimSpace(items[2]))
		table = append(table, [2]uint32{uint32(a), uint32(b)})
		total += float64(a-last) * float64(c) / math.MaxUint32
		last = a
	}
	lines := m.Line()
	fmt.Printf("The expected value %f.", total/float64(lines))
	fname := fmt.Sprintf("generated/GM%dCTL%d.go", gameID, ctl)
	var buf bytes.Buffer
	enc := gob.NewEncoder(&buf)
	err = enc.Encode(table)
	if err != nil {
		panic(err)
	}
	name := fmt.Sprintf("GM%dCTL%d", gameID, ctl)
	txt = fmt.Sprintf(`
	// Code generated by igame-gen. DO NOT EDIT.
	package generated

	import (
		"igameCommon/snapshoot"
	)

	var _ = snapshoot.Save("%s", %s)

	func %s() string {
		return "%s"
	}
	`, name, name, name, utils.CompressAndBase64(buf.Bytes()))
	os.WriteFile(fname, []byte(txt), 0755)
	exec.Command("go", "fmt", fname).Run()
}

func match(payout float64, limits map[float64][]float64) float64 {
	var upper float64 = math.MaxInt32
	for k := range limits {
		if k > payout {
			upper = min(upper, k)
		}
	}
	if upper == math.MaxInt32 {
		return -1
	}
	return upper
}

func stringf(n float64) string {
	return strconv.FormatFloat(n, 'f', -1, 64)
}
