package main

import (
	"fmt"
	"igame"
	"igameCommon/utils"
	"log"
	"os"
	"strconv"
	"time"
)

func main() {
	utils.Init()
	defer onExit()
	st := time.Now()
	gameID, err := strconv.Atoi(os.Args[1])
	if err != nil {
		panic(err)
	}
	b, err := os.ReadFile(fmt.Sprintf("configs/%d.yaml", gameID))
	if err != nil {
		panic(err)
	}
	igame.Create(int32(gameID), b)
	log.Printf("create completed, total elapsed time %v\n", time.Since(st))
}

func onExit() {
	utils.WriteLogs()
}
