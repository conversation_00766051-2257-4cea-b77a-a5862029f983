package main

import (
	"flag"
	"fmt"
	"igame"
	"igameCommon/basic"
	"igameCommon/utils"
	"math/rand"
	"strconv"
	"time"
)

func main() {
	st := time.Now()
	var gameID int32
	var ctl int32
	var tax int32
	flag.Parse()
	if len(flag.Args()) > 0 {
		n, _ := strconv.Atoi(flag.Arg(0))
		gameID = int32(n)
		n, _ = strconv.Atoi(flag.Arg(1))
		ctl = int32(n)
		n, _ = strconv.Atoi(flag.Arg(2))
		tax = int32(n)
	} else {
		gameID = 10000074
		ctl = 0
		tax = 2
	}
	igame.InitFromSnapshoot(gameID, ctl)
	fmt.Printf("gameID: %d, ctl: %d, tax: %d\n", gameID, ctl, tax)
	fmt.Printf("init duration %v\n", time.Since(st))
	st = time.Now()
	var total float64
	var freeTotal float64
	var specTotal float64
	var normTotal float64
	const testCnt = 10000000
	count := map[time.Duration]int{}
	var maxDuration time.Duration
	var freeTriggerCount, freeTotalPayout, freeTotalBet int64
	var specTriggerCount, specTotalPayout, specTotalBet int64
	var normTotalPayout, normTotalBet int64
	for i := range testCnt {
		since := time.Now()
		n := rand.Int63()
		spin, coef := igame.Spin(gameID, ctl, n, utils.Divide(tax, 100))
		lines := igame.Line(gameID)
		payout := spin.Payout()
		payouts := spin.RoundPayouts()
		var roundPayoutTotal int32 = 0
		for _, pay := range payouts {
			roundPayoutTotal += pay
		}
		if roundPayoutTotal != payout {
			s := fmt.Sprintf("payout error, payout: %v, roundPayoutTotal: %v, n: %v", payout, roundPayoutTotal, n)
			fmt.Println(s)
			panic(s)
		}
		tag := spin.Tags()[0]
		bet := int64(lines * coef)
		if tag == basic.EnumSpinTag.FREE {
			freeTriggerCount++
			freeTotalPayout += int64(payout)
			freeTotalBet += bet
			freeTotal += float64(payout) / float64(bet) * 100
		} else if tag == basic.EnumSpinTag.SPEC {
			specTriggerCount++
			specTotalPayout += int64(payout)
			specTotalBet += bet
			specTotal += float64(payout) / float64(bet) * 100
		} else {
			normTotalPayout += int64(payout)
			normTotalBet += bet
			normTotal += float64(payout) / float64(bet) * 100
		}
		total += float64(payout) / float64(bet) * 100
		if (i+1)%10000 == 0 {
			if freeTotal > 0 {
				fmt.Printf("free probability: %.4f%%, free count: %v, total count: %v\n", float64(freeTriggerCount)/float64(i+1)*100, freeTriggerCount, i+1)
				fmt.Printf("freeTotalPayout: %v, freeAvgPayout: %.4f, freeRTP: %v%%\n", freeTotalPayout, float64(freeTotalPayout)/float64(freeTriggerCount), freeTotal/float64(i+1)*100)
			}
			if specTotal > 0 {
				fmt.Printf("spec probability: %.4f%%, spec count: %v\n, total count: %v\n", float64(specTriggerCount)/float64(i+1)*100, specTriggerCount, i+1)
				fmt.Printf("specTotalPayout: %v, specAvgPayout: %.4f, specRTP: %v%%\n", specTotalPayout, float64(specTotalPayout)/float64(specTriggerCount), specTotal/float64(i+1)*100)
			}
			if normTotal > 0 {
				fmt.Printf("normTotalPayout: %v,  normRTP: %v%%\n", normTotalPayout, normTotal/float64(i+1)*100)
			}
			fmt.Printf("%.2f%% complete, RTP: %.4f%%\n\n", float64(i)/float64(testCnt)*100, total/float64(i+1)*100)
		}
		duration := time.Since(since)
		count[duration/time.Millisecond]++
		maxDuration = max(duration, maxDuration)
	}
	fmt.Println("100% complete")
	duration := time.Since(st)
	fmt.Printf("result num %d, total duration %v, avg %v, max %v\n", testCnt, duration, duration/time.Duration(total), maxDuration)
	utils.RangeMapOrdered(count, func(k time.Duration, n int) bool {
		fmt.Printf("[%v, %v): %d, %.2f %%\n", k*time.Millisecond, (k+1)*time.Millisecond, n, utils.Divide(n, total)*100)
		return false
	})
}
