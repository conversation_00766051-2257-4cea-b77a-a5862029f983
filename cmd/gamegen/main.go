package main

import (
	"fmt"
	"log"
	"os"
	"path/filepath"
	"strconv"
	"text/template"
	"time"
)

// GameConfig 游戏配置结构
type GameConfig struct {
	GameID      int32  `yaml:"gameId"`
	GameName    string `yaml:"gameName"`
	Line        int32  `yaml:"line"`
	Row         int32  `yaml:"row"`
	Column      int32  `yaml:"column"`
	MaxPayout   int64  `yaml:"maxPayout"`
	Author      string `yaml:"author"`
	Description string `yaml:"description"`
}

// TemplateData 模板数据结构
type TemplateData struct {
	GameID      string
	GameName    string
	Line        int32
	Row         int32
	Column      int32
	MaxPayout   int64
	Author      string
	Description string
	Timestamp   string
	Year        string
}

func main() {
	if len(os.Args) < 3 {
		fmt.Println("用法: gamegen <游戏ID> <游戏名称> [作者] [描述]")
		fmt.Println("示例: gamegen 500001 \"新老虎机\" \"开发者\" \"这是一个新的老虎机游戏\"")
		os.Exit(1)
	}

	gameIDStr := os.Args[1]
	gameName := os.Args[2]
	author := "Developer"
	description := "新生成的游戏项目"

	if len(os.Args) > 3 {
		author = os.Args[3]
	}
	if len(os.Args) > 4 {
		description = os.Args[4]
	}

	gameID, err := strconv.ParseInt(gameIDStr, 10, 32)
	if err != nil {
		log.Fatalf("无效的游戏ID: %v", err)
	}

	config := GameConfig{
		GameID:      int32(gameID),
		GameName:    gameName,
		Line:        20,
		Row:         3,
		Column:      5,
		MaxPayout:   10000000,
		Author:      author,
		Description: description,
	}

	if err := generateGameProject(config); err != nil {
		log.Fatalf("生成游戏项目失败: %v", err)
	}

	fmt.Printf("✅ 游戏项目 %s (ID: %d) 生成成功!\n", gameName, gameID)
	fmt.Printf("📁 模块文件: modules/m%d.go\n", gameID)
	fmt.Printf("⚙️  配置文件: bin/configs/%d.yaml\n", gameID)
	fmt.Printf("🚀 运行命令: cd bin && ./generate.exe %d\n", gameID)
}

func generateGameProject(config GameConfig) error {
	templateData := TemplateData{
		GameID:      fmt.Sprintf("%d", config.GameID),
		GameName:    config.GameName,
		Line:        config.Line,
		Row:         config.Row,
		Column:      config.Column,
		MaxPayout:   config.MaxPayout,
		Author:      config.Author,
		Description: config.Description,
		Timestamp:   time.Now().Format("2006-01-02 15:04:05"),
		Year:        time.Now().Format("2006"),
	}

	// 生成模块文件
	if err := generateModuleFile(templateData); err != nil {
		return fmt.Errorf("生成模块文件失败: %v", err)
	}

	// 生成配置文件
	if err := generateConfigFile(templateData); err != nil {
		return fmt.Errorf("生成配置文件失败: %v", err)
	}

	// 生成游戏数据结构文件
	if err := generateGameStructFile(templateData); err != nil {
		return fmt.Errorf("生成游戏数据结构文件失败: %v", err)
	}

	return nil
}

func generateModuleFile(data TemplateData) error {
	moduleTemplate := `package modules

import (
	"encoding/json"
	"igameCommon/basic"
	"igameCommon/utils"
	"igameHttp/games"
	"math/rand"
)

// {{.Description}}
// 游戏ID: {{.GameID}}
// 游戏名称: {{.GameName}}
// 作者: {{.Author}}
// 生成时间: {{.Timestamp}}

// 配置结构体
type c{{.GameID}} struct {
	MaxPayout   int64                 ` + "`yaml:\"maxPayout\"`" + `   // 最大赔付
	Line        int32                 ` + "`yaml:\"line\"`" + `        // 线数
	Row         int                   ` + "`yaml:\"row\"`" + `         // 行数
	Column      int                   ` + "`yaml:\"column\"`" + `      // 列数
	Pattern     [][]basic.Position    ` + "`yaml:\"pattern\"`" + `     // 连线模式
	PayoutTable map[int16][]int64     ` + "`yaml:\"payoutTable\"`" + ` // 赔付表
	IconWeight  map[int16]int32       ` + "`yaml:\"iconWeight\"`" + `  // 图标权重
	WildIcon    int16                 ` + "`yaml:\"wildIcon\"`" + `    // 百搭图标
	ScatterIcon int16                 ` + "`yaml:\"scatterIcon\"`" + ` // 散布图标
}

// 游戏模块结构体
var _ = Factory.reg(basic.NewGeneral[*m{{.GameID}}])

type m{{.GameID}} struct {
	Config       c{{.GameID}}
	RandByWeight *utils.RandomWeightPicker[int16, int32]
}

func (m *m{{.GameID}}) Init(config []byte) {
	m.Config = utils.ParseYAML[c{{.GameID}}](config)
	m.RandByWeight = utils.NewRandomWeightPicker(m.Config.IconWeight)
}

func (m m{{.GameID}}) ID() int32 {
	return {{.GameID}}
}

func (m m{{.GameID}}) Line() int32 {
	return m.Config.Line
}

func (m m{{.GameID}}) ClientMode() int32 {
	return basic.EnumClientMode.ONE
}

func (m m{{.GameID}}) Exception(code int32) string {
	s := &games.S{{.GameID}}{}
	return s.Exception(code)
}

func (m *m{{.GameID}}) ZeroSpin(ctl int32, rd *rand.Rand) basic.ISpin {
	for {
		spin := m.Spin(rd)
		if spin.Payout() == 0 {
			return spin
		}
	}
}

func (m *m{{.GameID}}) Spin(rd *rand.Rand) basic.ISpin {
	// 生成网格
	grid := m.generateGrid(rd)
	
	// 创建旋转结果
	spin := &games.S{{.GameID}}{
		Grid: grid,
		Pays: 0,
	}
	
	// 计算赔付
	m.calculatePayout(spin)
	
	return spin
}

func (m *m{{.GameID}}) generateGrid(rd *rand.Rand) []int16 {
	gridSize := m.Config.Row * m.Config.Column
	grid := make([]int16, gridSize)
	
	for i := 0; i < gridSize; i++ {
		grid[i] = m.RandByWeight.One(rd)
	}
	
	return grid
}

func (m *m{{.GameID}}) calculatePayout(spin *games.S{{.GameID}}) {
	// TODO: 实现赔付计算逻辑
	// 这里需要根据具体游戏规则实现
	spin.Pays = 0
}

func (m *m{{.GameID}}) Salting(spin basic.ISpin, salt *rand.Rand) basic.ISpin {
	s := spin.(*games.S{{.GameID}})
	s.GameId = m.ID()
	s.Line = m.Line()
	s.Row = m.Config.Row
	s.Column = m.Config.Column
	
	// 最大赔付限制
	if s.Pays > int32(m.Config.MaxPayout) {
		s.Pays = int32(m.Config.MaxPayout)
	}
	
	return s
}

func (m m{{.GameID}}) Rule() string {
	ruleData := map[string]interface{}{
		"gameId":     m.ID(),
		"gameName":   "{{.GameName}}",
		"line":       m.Config.Line,
		"row":        m.Config.Row,
		"column":     m.Config.Column,
		"maxPayout":  m.Config.MaxPayout,
		"wildIcon":   m.Config.WildIcon,
		"scatterIcon": m.Config.ScatterIcon,
	}
	
	b, _ := json.Marshal(ruleData)
	return string(b)
}

func (m m{{.GameID}}) InputCoef(ctl int32) int32 {
	return 100
}

func (m m{{.GameID}}) MinPayout(ctl int32) int32 {
	return 0
}
`

	// 创建模块文件
	moduleFile := filepath.Join("modules", fmt.Sprintf("m%s.go", data.GameID))

	tmpl, err := template.New("module").Parse(moduleTemplate)
	if err != nil {
		return err
	}

	file, err := os.Create(moduleFile)
	if err != nil {
		return err
	}
	defer file.Close()

	return tmpl.Execute(file, data)
}

func generateConfigFile(data TemplateData) error {
	configTemplate := `# {{.Description}}
# 游戏ID: {{.GameID}}
# 游戏名称: {{.GameName}}
# 作者: {{.Author}}
# 生成时间: {{.Timestamp}}

MaxPayout: {{.MaxPayout}}
Line: {{.Line}}
Row: {{.Row}}
Column: {{.Column}}

# 特殊图标配置
WildIcon: 11     # 百搭图标
ScatterIcon: 12  # 散布图标

# 连线模式 (示例：5x3网格的20线模式)
Pattern:
  - [21, 22, 23, 24, 25] # 1
  - [11, 12, 13, 14, 15] # 2
  - [31, 32, 33, 34, 35] # 3
  - [11, 22, 33, 24, 15] # 4
  - [31, 22, 13, 24, 35] # 5
  - [21, 32, 33, 34, 25] # 6
  - [21, 12, 13, 14, 25] # 7
  - [11, 22, 23, 24, 15] # 8
  - [31, 22, 23, 24, 35] # 9
  - [31, 32, 23, 14, 15] # 10
  - [11, 12, 23, 34, 35] # 11
  - [31, 22, 23, 24, 15] # 12
  - [11, 22, 23, 24, 35] # 13
  - [21, 22, 13, 24, 25] # 14
  - [21, 22, 33, 24, 25] # 15
  - [11, 12, 23, 14, 15] # 16
  - [31, 32, 23, 34, 35] # 17
  - [21, 32, 23, 34, 25] # 18
  - [11, 22, 13, 24, 15] # 19
  - [31, 22, 33, 24, 35] # 20

# 赔付表 (图标ID: [3连, 4连, 5连])
PayoutTable:
  1: [0, 0, 0, 100, 500]   # 高价值图标
  2: [0, 0, 0, 50, 250]    # 中价值图标
  3: [0, 0, 0, 30, 150]    # 中价值图标
  4: [0, 0, 0, 20, 100]    # 低价值图标
  5: [0, 0, 0, 15, 80]     # 低价值图标
  6: [0, 0, 0, 10, 60]     # 低价值图标
  7: [0, 0, 0, 8, 40]      # 低价值图标
  8: [0, 0, 0, 6, 30]      # 低价值图标
  9: [0, 0, 0, 4, 20]      # 低价值图标
  10: [0, 0, 0, 2, 10]     # 低价值图标

# 图标权重配置
IconWeight:
  1: 50    # 高价值图标 - 低权重
  2: 80    # 中价值图标
  3: 100   # 中价值图标
  4: 120   # 低价值图标
  5: 150   # 低价值图标
  6: 180   # 低价值图标
  7: 200   # 低价值图标
  8: 220   # 低价值图标
  9: 250   # 低价值图标
  10: 300  # 低价值图标 - 高权重
  11: 50   # 百搭图标 - 低权重
  12: 30   # 散布图标 - 低权重

# 样本配置
Limit:
  NORM:
    200: 10000
    100: 50000
    30: 50000
    20: 50000
    10: 50000
    7: 50000
    6: 50000
    4: 50000
    2: 50000

# 最小赔率配置
# 只配置非 NORM 样本的数据
MinLimit:
  1:
    X: 0 # 最小总线赔率值 总线 -> 单线 * 线数(Line)
    Limit:
       FREE: 100 # 最小赔率样本数
`

	// 创建配置文件目录
	configDir := filepath.Join("bin", "configs")
	if err := os.MkdirAll(configDir, 0755); err != nil {
		return err
	}

	// 创建配置文件
	configFile := filepath.Join(configDir, fmt.Sprintf("%s.yaml", data.GameID))

	tmpl, err := template.New("config").Parse(configTemplate)
	if err != nil {
		return err
	}

	file, err := os.Create(configFile)
	if err != nil {
		return err
	}
	defer file.Close()

	return tmpl.Execute(file, data)
}

func generateGameStructFile(data TemplateData) error {
	gameStructTemplate := `package games

import (
	"encoding/json"
	"igameCommon/basic"
)

// {{.Description}}
// 游戏ID: {{.GameID}}
// 游戏名称: {{.GameName}}
// 作者: {{.Author}}
// 生成时间: {{.Timestamp}}

// S{{.GameID}} 游戏旋转结果结构体
type S{{.GameID}} struct {
	GameId int32   ` + "`json:\"gameId\"`" + `     // 游戏ID
	Line   int32   ` + "`json:\"line\"`" + `       // 线数
	Row    int     ` + "`json:\"row\"`" + `        // 行数
	Column int     ` + "`json:\"column\"`" + `     // 列数
	Grid   []int16 ` + "`json:\"grid\"`" + `       // 网格数据
	Pays   int32   ` + "`json:\"pays\"`" + `       // 总赔付
	Lines  []LineInfo{{.GameID}} ` + "`json:\"lines,omitempty\"`" + ` // 中奖线信息
}

// LineInfo{{.GameID}} 中奖线信息
type LineInfo{{.GameID}} struct {
	ID     int   ` + "`json:\"id\"`" + `       // 线号
	Icon   int16 ` + "`json:\"icon\"`" + `     // 中奖图标
	Count  int   ` + "`json:\"count\"`" + `    // 连线数量
	Win    int64 ` + "`json:\"win\"`" + `      // 中奖金额
	Positions []int ` + "`json:\"positions\"`" + ` // 中奖位置
}

// 实现 ISpin 接口
func (s *S{{.GameID}}) Payout() int32 {
	return s.Pays
}

func (s *S{{.GameID}}) Exception(code int32) string {
	switch code {
	case 1:
		return ` + "`{\"error\":\"insufficient_balance\"}`" + `
	default:
		return ` + "`{\"error\":\"unknown\"}`" + `
	}
}

func (s *S{{.GameID}}) RoundPayouts() []int32 {
	return []int32{s.Pays}
}

func (s *S{{.GameID}}) Data(ctx basic.SpinContext) string {
	data := map[string]interface{}{
		"gameId": s.GameId,
		"line":   s.Line,
		"row":    s.Row,
		"column": s.Column,
		"grid":   s.Grid,
		"pays":   s.Pays,
		"lines":  s.Lines,
	}

	b, _ := json.Marshal(data)
	return string(b)
}

// 构建 box
func (s *S{{.GameID}}) buildBox(grid []int16) [][]int16 {
	box := make([][]int16, s.Row)
	for i := 0; i < s.Row; i++ {
		box[i] = make([]int16, s.Column)
	}
	for i := 0; i < s.Row; i++ {
		for j := 0; j < s.Column; j++ {
			box[i][j] = grid[i*s.Column+j]
		}
	}
	return box
}

// 构建 mask
func (s *S{{.GameID}}) buildMask(wilds map[int]int16) [][]int {
	mask := make([][]int, s.Row)
	for i := 0; i < s.Row; i++ {
		mask[i] = make([]int, s.Column)
	}
	for i := 0; i < s.Row; i++ {
		for j := 0; j < s.Column; j++ {
			index := i*s.Column + j
			mask[i][j] = s.WildMap[wilds[index]]
		}
	}
	return mask
}

// 找出指定图标的位置
func (s *S{{.GameID}}) shotsList(grid []int16) []map[string]any {
	shotsList := []map[string]any{}
	for i := 0; i < s.Row; i++ {
		for j := 0; j < s.Column; j++ {
			index := i*s.Column + j
			// TODO: 自己实现判断条件
			if grid[index] > 14 {
				shotsList = append(shotsList, map[string]any{
					"r": i,
					"c": j,
				})
			}
		}
	}
	return shotsList
}

// 找出 grid 中的 scatter
func (s *S{{.GameID}}) scattersInfo(grid []int16) []belatra.ScattersInfo {
	scattersInfo := []belatra.ScattersInfo{}
	scatterCount := 0
	hotScatterCount := 0
	for i := 0; i < s.Row; i++ {
		for j := 0; j < s.Column; j++ {
			index := i*s.Column + j
			switch grid[index] {
			// TODO: 自己实现判断条件
			case 12:
				scatterCount++
			case 13:
				hotScatterCount++
			}
		}
	}
	// TODO: 自己实现判断条件
	if scatterCount > 0 {
		scattersInfo = append(scattersInfo, belatra.ScattersInfo{N: scatterCount, Win: 0, ScId: 12})
	}
	if hotScatterCount > 0 {
		scattersInfo = append(scattersInfo, belatra.ScattersInfo{N: hotScatterCount, Win: 0, ScId: 13})
	}
	return scattersInfo
}

// 更新中奖线信息
func (s *S{{.GameID}}) updateLinesInfo(lines []belatra.LinesInfo, inputLine float64) []belatra.LinesInfo {
	linesInfo := []belatra.LinesInfo{}
	for _, line := range lines {
		line.Iwin.Win = line.Iwin.Win * int64(inputLine)
		linesInfo = append(linesInfo, line)
	}
	return linesInfo
}

func (s *S{{.GameID}}) RoundData(index int32, ctx basic.SpinContext) string {
	return s.Data(ctx)
}

func (s *S{{.GameID}}) Tags() []string {
	if s.Pays > 0 {
		return []string{"WIN", "NORM"}
	}
	return []string{"NORM"}
}

func (s *S{{.GameID}}) Tag() string {
	return s.Tags()[0]
}
`

	// 创建游戏数据结构文件目录
	gameDir := filepath.Join("http", "games")
	if err := os.MkdirAll(gameDir, 0755); err != nil {
		return err
	}

	// 创建游戏数据结构文件
	gameFile := filepath.Join(gameDir, fmt.Sprintf("s%s.go", data.GameID))

	tmpl, err := template.New("gamestruct").Parse(gameStructTemplate)
	if err != nil {
		return err
	}

	file, err := os.Create(gameFile)
	if err != nil {
		return err
	}
	defer file.Close()

	return tmpl.Execute(file, data)
}
