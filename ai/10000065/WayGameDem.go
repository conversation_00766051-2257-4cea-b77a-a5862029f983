package modules

import (
	"encoding/json"
	"igameCommon/basic"
	"math/rand"
)

// Ways玩法老虎机游戏框架模块
type WaysGame struct {
	Config WaysConfig // 游戏配置
}

// 游戏配置结构体
type WaysConfig struct {
	Row         int32           // 行数
	Column      int32           // 列数
	WildIcon    int32           // Wild符号ID
	PayoutTable [][3]int32      // 赔率表 [符号ID, 连线数, 赔率*100]
	FreeSpin    FreeSpinConfig  // 免费旋转配置
	BaseCoef    []int32         // 普通模式级联乘数
	FreeCoef    []int32         // 免费旋转模式级联乘数
	MaxPayout   int32           // 最大派彩
	IconWeight  map[int32]int32 // 符号权重
}

// 免费旋转配置
type FreeSpinConfig struct {
	Icon       int32 // 触发免费旋转的符号ID
	Number     int   // 触发免费旋转的最小符号数
	FirstCount int   // 首次免费旋转次数
	MoreCount  int   // 额外触发免费旋转的次数
}

// 返回游戏ID
func (g *WaysGame) ID() int32 {
	return 10000000 // 默认ID，可根据具体游戏修改
}

// 返回线数（Ways玩法通常为1）
func (g *WaysGame) Line() int32 {
	return 1
}

// 初始化游戏配置
func (g *WaysGame) Init(config []byte) {
	g.Config = ParseConfig(config) // 假设有一个ParseConfig函数解析配置
}

// 返回游戏规则的JSON字符串
func (g *WaysGame) Rule() string {
	b, _ := json.Marshal(g.Config)
	return string(b)
}

// 返回输入系数
func (g *WaysGame) InputCoef(ctl int32) int32 {
	return 100 // 默认系数，可根据需求调整
}

// 处理旋转结果的加盐逻辑
func (g *WaysGame) Salting(spin0 basic.ISpin, salt *rand.Rand, ctx basic.SpinContext) basic.ISpin {
	// 加盐逻辑待实现，返回旋转结果
	return spin0
}

// 执行一次旋转
func (g *WaysGame) Spin(rd *rand.Rand) basic.ISpin {
	page := g.generatePage(rd, false) // 生成普通模式页面
	totalPay := page.Pay
	if totalPay > g.Config.MaxPayout {
		return &SpinResult{Pays: -1} // 超过最大派彩返回无效结果
	}
	spin := &SpinResult{
		Pays:   totalPay,
		Rounds: []WaysPage{page},
	}
	// 检查是否触发免费旋转
	if freeSpins := g.checkFreeSpins(page); freeSpins > 0 {
		for i := 0; i < freeSpins; i++ {
			page = g.generatePage(rd, true)
			totalPay = page.Pay
			if totalPay > g.Config.MaxPayout {
				return &SpinResult{Pays: -1}
			}
			spin.Rounds = append(spin.Rounds, page)
			spin.Pays += totalPay
		}
	}
	return spin
}

// 生成一个页面
func (g *WaysGame) generatePage(rd *rand.Rand, isFree bool) WaysPage {
	cols, rows := int(g.Config.Column), int(g.Config.Row)
	grid := g.generateGrid(cols, rows, rd, isFree) // 生成初始网格
	cascadeCount := 0
	page := WaysPage{}
	for {
		wins := g.calcWays(grid) // 计算中奖
		basicPays := int32(0)
		for _, win := range wins {
			basicPays += win.WinAmount
		}
		coef := g.getCascadingMultiplier(isFree, cascadeCount)
		singlePage := SinglePage{
			BasicPays: basicPays,
			Coef:      coef,
			Win:       wins,
		}
		page.Pages = append(page.Pages, singlePage)
		page.Pay += basicPays * coef
		if len(wins) == 0 {
			break // 无中奖则结束级联
		}
		cascadeCount++
		grid = g.dropSymbols(grid, wins, rd) // 处理符号掉落
	}
	return page
}

// 计算Ways中奖
func (g *WaysGame) calcWays(grid [][]int32) []WinInfo {
	var wins []WinInfo
	for symbol := int32(1); symbol <= 10; symbol++ { // 假设符号ID范围
		ways := 1
		var positions []Position
		line := 0
		for col := 0; col < len(grid); col++ {
			count := 0
			for row := 0; row < len(grid[col]); row++ {
				if grid[col][row] == symbol || grid[col][row] == g.Config.WildIcon {
					count++
					positions = append(positions, Position{Col: col, Row: row})
				}
			}
			if count > 0 {
				line++
				ways *= count
			} else {
				break
			}
		}
		if line >= 3 { // 至少3列连线
			winAmount := g.getPayout(symbol, line)
			wins = append(wins, WinInfo{
				Symbol:    symbol,
				Ways:      ways,
				WinAmount: winAmount * int32(ways),
				Positions: positions,
			})
		}
	}
	return wins
}

// 获取赔率
func (g *WaysGame) getPayout(symbol int32, count int) int32 {
	for _, payout := range g.Config.PayoutTable {
		if payout[0] == symbol && int(payout[1]) == count {
			return payout[2]
		}
	}
	return 0
}

// 获取级联乘数
func (g *WaysGame) getCascadingMultiplier(isFree bool, cascadeCount int) int32 {
	coef := g.Config.BaseCoef
	if isFree {
		coef = g.Config.FreeCoef
	}
	if cascadeCount < len(coef) {
		return coef[cascadeCount]
	}
	return coef[len(coef)-1]
}

// 检查是否触发免费旋转
func (g *WaysGame) checkFreeSpins(page WaysPage) int {
	lastPage := page.Pages[len(page.Pages)-1]
	scatterCount := 0
	for _, win := range lastPage.Win {
		if win.Symbol == g.Config.FreeSpin.Icon {
			scatterCount += len(win.Positions)
		}
	}
	if scatterCount >= g.Config.FreeSpin.Number {
		return g.Config.FreeSpin.FirstCount + (scatterCount-g.Config.FreeSpin.Number)*g.Config.FreeSpin.MoreCount
	}
	return 0
}

// 生成初始网格
func (g *WaysGame) generateGrid(cols, rows int, rd *rand.Rand, isFree bool) [][]int32 {
	// 实现随机生成网格逻辑，可参考原文件
	return nil // 占位，需根据具体需求实现
}

// 处理符号掉落
func (g *WaysGame) dropSymbols(grid [][]int32, wins []WinInfo, rd *rand.Rand) [][]int32 {
	// 实现符号消除和掉落逻辑，可参考原文件
	return nil // 占位，需根据具体需求实现
}

// 生成不中奖的旋转
func (g *WaysGame) ZeroSpin(ctl int32, rd *rand.Rand) basic.ISpin {
	for {
		spin := g.Spin(rd)
		if spin.Payout() == 0 {
			return spin
		}
	}
}

// 旋转结果结构体
type SpinResult struct {
	Pays   int32      // 总赔率
	Rounds []WaysPage // 每轮数据
}

func (r *SpinResult) Payout() int32 {
	return r.Pays
}

func (r *SpinResult) RoundPayouts() []int32 {
	return []int32{r.Payout()}
}

func (r *SpinResult) Data(ctx basic.SpinContext) string {
	return "" // 返回前端数据，需根据需求实现
}

func (r *SpinResult) RoundData(index int32, ctx basic.SpinContext) string {
	return r.Data(ctx)
}

func (r *SpinResult) Tags() []string {
	return nil
}

func (r *SpinResult) Exception(code int32) string {
	return ""
}

// 页面结构体
type WaysPage struct {
	Pay   int32        // 赔率
	Pages []SinglePage // 每轮中的每一页
}

// 单页结构体
type SinglePage struct {
	BasicPays int32     // 基础赔率（未包含级联乘数）
	Coef      int32     // 级联乘数
	Win       []WinInfo // 中奖信息
}

// 中奖信息结构体
type WinInfo struct {
	Symbol    int32      // 图标
	Ways      int        // 路数
	WinAmount int32      // 赔率（未乘级联乘数）
	Positions []Position // 位置
}

// 位置结构体
type Position struct {
	Col int // 列
	Row int // 行
}

// 假设的配置解析函数
func ParseConfig(config []byte) WaysConfig {
	// 实现配置解析逻辑
	return WaysConfig{}
}
