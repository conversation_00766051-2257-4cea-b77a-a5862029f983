# 麻将胡了1 配置文件

MaxPayout: 250000   # 最高奖金为 250000

FreeSpin:
  Icon: 10           # “胡”作为 Scatter 触发免费旋转 （免费旋转符号，胡牌图标）
  Number: 3          # 3个 Scatter 触发免费旋转
  FirstCount: 12     # 3个Scatter触发旋转次数为 12 次（3个Scatter触发）
  MoreCount: 2       # 每增加1个Scatter增加2次免费旋转

Row: 6               # 游戏行数为 4
Column: 5            # 游戏列数为 5
GoldWeight: 10       # 金色掉落概率 10分之10
WildIcon: 9          # wildIcon

PayoutTable:
  # 格式: [图标ID, 连线数, 倍率百分比]
  # 注：倍率按“倍数”表示，即10x写为1000，25x写为2500，50x写为5000，依此类推
  # 发 (8)
  - [8, 3, 15]    # 3连：15x
  - [8, 4, 60]   # 4连：60x
  - [8, 5, 100]   # 5连：100x
  # 中 (7)
  - [7, 3, 10]    # 3连：10x
  - [7, 4, 40]   # 4连：40x
  - [7, 5, 80]   # 5连：80x
  # 白板 (6)
  - [6, 3, 8]    # 3连：8x
  - [6, 4, 20]   # 4连：20x
  - [6, 5, 60]   # 5连：60x
  # 八万 (5)
  - [5, 3, 6]    # 3连：6x
  - [5, 4, 15]   # 4连：15x
  - [5, 5, 40]   # 5连：40x
  # 五筒 (4)
  - [4, 3, 4]    # 3连：4x
  - [4, 4, 10]    # 4连：10x
  - [4, 5, 20]   # 5连：20x
  # 五条 (3)
  - [3, 3, 4]    # 3连：4x
  - [3, 4, 10]    # 4连：10x
  - [3, 5, 20]   # 5连：20x
  # 二筒 (2)
  - [2, 3, 2]    # 3连：2x
  - [2, 4, 5]    # 4连：5x
  - [2, 5, 10]    # 5连：10x
  # 二条 (1)
  - [1, 3, 2]    # 3连：2x
  - [1, 4, 5]    # 4连：5x
  - [1, 5, 10]    # 5连：10x

BaseCoef:
  # 普通模式下，滚轴消除后递增的乘数变化（初始为 x1）
  - 1   # 第一次 cascading：乘数为1
  - 2   # 第二次 cascading：乘数从 x1 提升至 x2
  - 3   # 第三次 cascading：乘数从 x2 提升至 x3
  - 5   # 第四次 cascading：乘数从 x3 提升至 x5

FreeCoef:
  # 免费旋转模式下，滚轴消除后递增的乘数变化（初始为 x2）
  - 2   # 第一次 cascading：乘数从 x1 提升至 x2
  - 4   # 第二次 cascading：乘数从 x2 提升至 x4
  - 6   # 第三次 cascading：乘数从 x4 提升至 x6
  - 10  # 第四次 cascading：乘数从 x6 提升至 x10

IconWeight:
  # 各图标出现权重
  1: 100 #二条
  2: 100 #二筒
  3: 100 #五条
  4: 100 #五筒
  5: 100 #八万
  6: 100 #白板
  7: 100 #中
  8: 100 #发
  9: 0 #作为Wild的图标（不直接出现）
  10: 30 #胡 (同时作为Scatter)

Limit:
  FREE:
    300: 100
    200: 100
    100: 100
    30: 10
    15: 0
  NORM:
    300: 10000
    200: 10000
    100: 10000
    30: 60000
    10: 25000
