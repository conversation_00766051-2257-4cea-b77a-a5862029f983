麻将胡了（Mahjong Ways）游戏规则
1. 基本游戏结构
网格结构：游戏采用 5列 × 6行 布局，总共有 30个格子。
连线计算范围：
仅第2~5行 参与 Ways 计算（即 中间4行）。
第1行（顶部） 和 第6行（底部） 仅用于掉落，不影响连线。
下注方式：
采用 Ways 玩法，符号从左到右连续排列即可中奖。
中奖的 Ways 数量 = 各列中奖符号的个数相乘（如：第1列2个、第2列3个、第3列1个，则 Ways 数 = 2×3×1 = 6）。
2. 符号规则
普通符号（1~9）：
每个符号的赔率在 PayoutTable 里定义，3连、4连、5连有不同倍率。
特殊符号：
Wild（百搭符号）：
可代替除 Scatter 以外的所有符号，以帮助形成连线组合。
Scatter（夺宝符号）：
可出现在 任意列（第1~5列均可）。
3个 Scatter 触发 12次免费旋转，额外 Scatter 每增加1个，额外增加2次免费旋转。
3. 金色麻将规则
在任何旋转期间：
第2、第3或第4列 上的某些符号（不包括 Wild 和 Scatter）有一定概率显示为金色麻将符号。
金色麻将符号转换：
在 新符号跌落后的每一轮旋转中：
上一轮赢奖中的金色麻将符号 将会 转换为 Wild（百搭符号）。
这样可以提高后续级联的中奖概率。
4. 中奖规则（Ways 计算）
符号必须从左到右连续出现，不可跨列断开，否则不算连线。
Ways 计算方式：
中奖组合的 Ways 数 = 每列中奖符号的数量相乘。
例如：
复制
编辑
第1列：2个相同符号
第2列：3个相同符号
第3列：1个相同符号
→ Ways 数 = 2 × 3 × 1 = 6
最少3列才能中奖：
若符号仅在前2列连续，不算中奖。
5. 级联消除（Cascading）
中奖后，符号会被消除，上方符号掉落填补空缺：
第1行（顶部）符号掉落至第2行，作为新的游戏符号。
第6行（底部）补充新符号，但不会影响掉落逻辑。
级联过程会持续，直到没有新的连线中奖为止。
金色麻将转换：
在新一轮掉落开始前，上一轮赢奖中涉及的金色麻将符号 会变成 Wild（百搭符号），提升后续连线概率。
6. 乘数系统
普通模式：
初始乘数为 x1，每次 Cascading（级联）后乘数递增：
复制
编辑
第1次 Cascading → x1
第2次 Cascading → x2
第3次 Cascading → x3
第4次及以上 → x5
免费旋转模式：
初始乘数为 x2，每次 Cascading 后乘数提升：
复制
编辑
第1次 Cascading → x2
第2次 Cascading → x4
第3次 Cascading → x6
第4次及以上 → x10
7. 掉落机制
中奖符号消除后，所有上方符号向下掉落，填补空缺。
顶部行（第1行）符号会掉落到第2行，作为新游戏符号。
底部行（第6行）用于生成新符号，不会影响掉落逻辑。
如果上一轮的金色麻将符号参与了中奖，在新符号掉落前，这些金色麻将符号会变成 Wild（百搭符号），以提高后续级联的中奖概率。
8. 免费旋转模式
3个 Scatter 触发 12 次免费旋转，每多1个 Scatter 额外 +2 次。
免费模式下，乘数起始为 x2，且级联时乘数提升更快：
普通模式：x1 → x2 → x3 → x5
免费模式：x2 → x4 → x6 → x10
免费旋转中可以继续触发免费旋转。
总结
✅ 采用 Ways 玩法，符号必须从左到右连续，最少3列才能中奖
✅ Scatter 可出现在任意列，不再限定于第2、3、4列
✅ 新增金色麻将规则，提高后续级联的中奖概率
✅ 中奖符号消除后，顶部符号掉落，底部补充新符号
✅ 普通模式乘数 x1x5，免费模式乘数 x2x10
✅ 免费旋转可再次触发，并带更高的级联乘数
这就是 麻将胡了 游戏规则！🎰🚀