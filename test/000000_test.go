package test

import (
	"flag"
	"fmt"
	"igame"
	"igameCommon/basic"
	"igameCommon/utils"
	"math/rand"
	"strconv"
	"sync"
	"testing"
	"time"
)

func getContext() basic.SpinContext {
	var balance float64 = 10000
	var Gamble float64 = 1
	var lv int32 = 1
	ctx := basic.SpinContext{}
	var input float64 = float64(Gamble) * float64(lv) * float64(20)
	ctx["gamble"] = Gamble
	ctx["lv"] = lv
	ctx["input"] = input
	ctx["balance"] = balance
	ctx["oracleKey"] = "0xdfasdfsafdsafd"
	ctx["currency"] = "10000"
	ctx["uuid"] = "123456"
	return ctx
}

func BenchmarkSpin(b *testing.B) {
	st := time.Now()
	var n int
	if len(flag.Args()) > 0 {
		n, _ = strconv.Atoi(flag.Arg(0))
	} else {
		n = 100001
	}
	gameID := int32(n)
	m := igame.GetModuleFromSnapshoot(gameID)
	b.Logf("init duration %v", time.Since(st))
	b.StartTimer()
	st = time.Now()
	spinIDs := utils.MapKeysOrdered(m.PayTable())
	var maxDuration time.Duration
	count := map[time.Duration]int{}
	for i, spinID := range spinIDs {
		since := time.Now()
		m.Spin(spinID)
		if (i+1)%10000 == 0 {
			b.Logf("%.2f%% complete", float64(i)/float64(len(spinIDs))*100)
		}
		duration := time.Since(since)
		count[duration/time.Millisecond]++
		maxDuration = max(duration, maxDuration)
	}
	b.Logf("100%% complete")
	duration := time.Since(st)
	total := len(spinIDs) + 1
	b.Logf("result num %d, total duration %v, avg %v, max %v", total, duration, duration/time.Duration(total), maxDuration)
	utils.RangeMapOrdered(count, func(k time.Duration, n int) bool {
		b.Logf("[%v, %v): %d, %.2f %%\n", k*time.Millisecond, (k+1)*time.Millisecond, n, utils.Divide(n, total)*100)
		return false
	})
	b.StopTimer()
}

func BenchmarkTax(b *testing.B) {
	st := time.Now()
	var gameID int32 = 100005
	var ctl int32 = 1
	var tax int32 = 2
	if len(flag.Args()) > 0 {
		n, _ := strconv.Atoi(flag.Arg(0))
		gameID = int32(n)
		n, _ = strconv.Atoi(flag.Arg(1))
		ctl = int32(n)
		n, _ = strconv.Atoi(flag.Arg(2))
		tax = int32(n)
	}
	igame.InitFromSnapshoot(gameID, ctl)
	b.Logf("init duration %v", time.Since(st))
	b.StartTimer()
	st = time.Now()
	var total float64
	const testCnt = 10000000
	const concurrency = 1000
	count := map[time.Duration]int{}
	var maxDuration time.Duration

	// 创建结果通道和等待组
	resultChan := make(chan float64, testCnt)
	var wg sync.WaitGroup

	// 启动1000个goroutine
	for i := 0; i < concurrency; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			perGoroutine := testCnt / concurrency
			for j := 0; j < perGoroutine; j++ {
				since := time.Now()
				n := rand.Int63()
				spin, coef := igame.Spin(gameID, ctl, n, utils.Divide(tax, 100))
				lines := igame.Line(gameID)
				payout := spin.Payout()
				payouts := spin.RoundPayouts()
				var roundPayoutTotal int32 = 0
				for _, pay := range payouts {
					roundPayoutTotal += pay
				}
				if roundPayoutTotal != payout {
					s := fmt.Sprintf("payout error, payout: %v, roundPayoutTotal: %v, n: %v", payout, roundPayoutTotal, n)
					b.Errorf(s)
					close(resultChan)
					panic(s)
				}
				result := float64(payout) / float64(lines*coef) * 100
				resultChan <- result

				duration := time.Since(since)
				maxDuration = max(duration, maxDuration)
			}
		}()
	}

	// 启动一个goroutine来关闭结果通道
	go func() {
		wg.Wait()
		close(resultChan)
	}()

	// 收集结果
	completed := 0
	for result := range resultChan {
		total += result
		completed++
		if completed%10000 == 0 {
			b.Logf("%.2f%% complete, tax: %.4f%%", float64(completed)/float64(testCnt)*100, total/float64(completed)*100)
		}
	}

	b.Logf("100%% complete")
	duration := time.Since(st)
	b.Logf("result num %d, total duration %v, avg %v, max %v", testCnt, duration, duration/time.Duration(testCnt), maxDuration)
	utils.RangeMapOrdered(count, func(k time.Duration, n int) bool {
		b.Logf("[%v, %v): %d, %.2f %%\n", k*time.Millisecond, (k+1)*time.Millisecond, n, utils.Divide(n, testCnt)*100)
		return false
	})
	b.StopTimer()
}
