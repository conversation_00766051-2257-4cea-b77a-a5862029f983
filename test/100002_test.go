package test

import (
	"igame"
	"testing"
	"time"
)

func Benchmark100002(b *testing.B) {
	st := time.Now()
	m := igame.GetModuleFromSnapshoot(100002)
	b.Logf("init duration %v", time.Since(st))
	b.StartTimer()
	st = time.Now()
	spinIDs := igame.SpinIDs(100002)
	for i, spinID := range spinIDs {
		m.Spin(spinID)
		if i%10000 == 0 {
			b.Logf("%.2f%% complete", float64(i)/float64(len(spinIDs))*100)
		}
	}
	b.Logf("test num %d, duration %v", b.N, time.Since(st))
	b.StopTimer()
}
