package test

import (
	"igame"
	"testing"
	"time"
)

func Benchmark100001(b *testing.B) {
	st := time.Now()
	var gameID int32 = 100001
	m := igame.GetModuleFromSnapshoot(gameID)
	b.Logf("init duration %v", time.Since(st))
	b.StartTimer()
	st = time.Now()
	spinIDs := igame.SpinIDs(gameID)
	for i, spinID := range spinIDs {
		m.Spin(spinID)
		if i%10000 == 0 {
			b.Logf("%.2f%% complete", float64(i)/float64(len(spinIDs))*100)
		}
	}
	b.Logf("test num %d, duration %v", b.N, time.Since(st))
	b.<PERSON>Timer()
}
