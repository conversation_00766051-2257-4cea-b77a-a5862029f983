# Makefile

PHONY: gen test pprof

gen: 
	@cd bin & go run cmd/generate/main.go $(id)

pprof:
	@go tool pprof -http=:8080 logs/cpu.pprof

test: force
	@go test -bench=BenchmarkSpin -timeout 10s -v ./test -args $(id)

testtax: force
	@go test -bench=BenchmarkTax -timeout 10000s -v ./test -args $(id) $(ctl) $(tax)

force:  # 强制执行
	@true

build:
	GOOS=windows GOARCH=amd64 go build -tags exe -o bin/generate.exe cmd/generate/main.go
	GOOS=windows GOARCH=amd64 go build -tags exe -o bin/paytab.exe cmd/paytab/main.go

request:
	@curl -X POST "localhost:9528" -H 'Content-Type: application/json' -d '{"GameID": $(id), "SpinID": $(spin)}'

paytab:
	@cd bin & go run ../cmd/paytab/main.go $(id) $(ctl)

simulate:
	@cd bin & go run ../cmd/simulate/main.go $(id) $(ctl) $(tax) $(n)

service:
	@cd bin & go run cmd/service/main.go $(id)